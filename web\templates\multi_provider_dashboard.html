<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{.title}}</title>
    <link rel="icon" type="image/svg+xml" href="/favicon.svg">
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js" defer></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        [x-cloak] { display: none !important; }
    </style>
</head>
<body class="bg-gray-100 min-h-screen">
    <div class="container mx-auto px-4 py-8" x-data="multiProviderDashboard()">
        <!-- Header -->
        <div class="flex justify-between items-center mb-8">
            <div>
                <h1 class="text-3xl font-bold text-gray-800">TurnsAPI 仪表板</h1>
                <p class="text-gray-600">管理多个AI提供商的密钥</p>
            </div>
            <div class="flex space-x-4">
                <a href="/" class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg transition duration-200">
                    返回首页
                </a>
                <a href="/logs" class="bg-purple-500 hover:bg-purple-600 text-white px-4 py-2 rounded-lg transition duration-200">
                    请求日志
                </a>
                <button @click="refreshData()" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg transition duration-200">
                    刷新数据
                </button>
                <button onclick="logout()" class="bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded-lg transition duration-200">
                    登出
                </button>
            </div>
        </div>

        <!-- System Overview - 第一排 -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
            <!-- 系统状态 -->
            <div class="bg-white rounded-xl shadow-sm border border-gray-100 p-6 hover:shadow-md transition-all duration-200">
                <div class="flex items-center justify-between mb-4">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="w-12 h-12 rounded-lg flex items-center justify-center" :class="systemHealth.status === 'healthy' ? 'bg-green-100' : systemHealth.status === 'degraded' ? 'bg-yellow-100' : 'bg-red-100'">
                                <svg class="w-6 h-6" :class="systemHealth.status === 'healthy' ? 'text-green-600' : systemHealth.status === 'degraded' ? 'text-yellow-600' : 'text-red-600'" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                            </div>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-500">系统状态</p>
                            <p class="text-xl font-bold" :class="systemHealth.status === 'healthy' ? 'text-green-600' : systemHealth.status === 'degraded' ? 'text-yellow-600' : 'text-red-600'" x-text="getStatusText(systemHealth.status)"></p>
                        </div>
                    </div>
                    <button @click="loadSystemHealth()" :disabled="loadingSystemHealth" class="inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded-md text-gray-700 bg-gray-100 hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 disabled:opacity-50 transition-all duration-200">
                        <svg x-show="!loadingSystemHealth" class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                        </svg>
                        <svg x-show="loadingSystemHealth" class="animate-spin w-3 h-3 mr-1" fill="none" viewBox="0 0 24 24">
                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        <span x-show="!loadingSystemHealth">刷新</span>
                        <span x-show="loadingSystemHealth">刷新中</span>
                    </button>
                </div>
                <div class="space-y-2">
                    <div class="flex justify-between text-sm">
                        <span class="text-gray-500">CPU使用率</span>
                        <span class="font-medium" x-text="formatPercentage(systemHealth.cpu_usage)">0%</span>
                    </div>
                    <div class="flex justify-between text-sm">
                        <span class="text-gray-500">内存使用</span>
                        <span class="font-medium" x-text="formatPercentage(systemHealth.memory_usage)">0%</span>
                    </div>
                    <div class="flex justify-between text-sm">
                        <span class="text-gray-500">最后检查</span>
                        <span class="font-medium" x-text="formatDate(systemHealth.last_check)">-</span>
                    </div>
                </div>
            </div>

            <!-- 提供商分组 -->
            <div class="bg-white rounded-xl shadow-sm border border-gray-100 p-6 hover:shadow-md transition-all duration-200">
                <div class="flex items-center justify-between mb-4">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                                <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
                                </svg>
                            </div>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-500">提供商分组</p>
                            <p class="text-xl font-bold text-blue-600" x-text="systemHealth.healthy_groups + '/' + systemHealth.total_groups"></p>
                        </div>
                    </div>
                    <button @click="loadProviderStatuses()" :disabled="loadingProviderStatuses" class="inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded-md text-gray-700 bg-gray-100 hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 disabled:opacity-50 transition-all duration-200">
                        <svg x-show="!loadingProviderStatuses" class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                        </svg>
                        <svg x-show="loadingProviderStatuses" class="animate-spin w-3 h-3 mr-1" fill="none" viewBox="0 0 24 24">
                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        <span x-show="!loadingProviderStatuses">刷新</span>
                        <span x-show="loadingProviderStatuses">刷新中</span>
                    </button>
                </div>
                <div class="space-y-2">
                    <div class="flex justify-between text-sm">
                        <span class="text-gray-500">健康分组</span>
                        <span class="font-medium text-green-600" x-text="systemHealth.healthy_groups || 0">0</span>
                    </div>
                    <div class="flex justify-between text-sm">
                        <span class="text-gray-500">异常分组</span>
                        <span class="font-medium text-red-600" x-text="(systemHealth.total_groups || 0) - (systemHealth.healthy_groups || 0)">0</span>
                    </div>
                    <div class="flex justify-between text-sm">
                        <span class="text-gray-500">平均响应</span>
                        <span class="font-medium" x-text="formatResponseTime(averageResponseTime)">-</span>
                    </div>
                </div>
            </div>

            <!-- 运行时间 -->
            <div class="bg-white rounded-xl shadow-sm border border-gray-100 p-6 hover:shadow-md transition-all duration-200">
                <div class="flex items-center justify-between mb-4">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center">
                                <svg class="w-6 h-6 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                            </div>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-500">运行时间</p>
                            <p class="text-xl font-bold text-orange-600" x-text="formatDuration(systemHealth.uptime)"></p>
                        </div>
                    </div>
                </div>
                <div class="space-y-2">
                    <div class="flex justify-between text-sm">
                        <span class="text-gray-500">启动时间</span>
                        <span class="font-medium" x-text="formatDate(systemHealth.start_time)">-</span>
                    </div>
                    <div class="flex justify-between text-sm">
                        <span class="text-gray-500">版本信息</span>
                        <span class="font-medium" x-text="systemHealth.version || 'v2.2.0'">v2.2.0</span>
                    </div>
                    <div class="flex justify-between text-sm">
                        <span class="text-gray-500">请求总数</span>
                        <span class="font-medium" x-text="systemHealth.total_requests || 0">0</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- API密钥状态 - 第二排 -->
        <div class="mb-8">
            <div class="bg-white rounded-xl shadow-sm border border-gray-100 p-6 hover:shadow-md transition-all duration-200">
                <div class="flex items-center justify-between mb-6">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                                <svg class="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 7a2 2 0 012 2m0 0a2 2 0 012 2v6a2 2 0 01-2 2H9a2 2 0 01-2-2V9a2 2 0 012-2m0 0V7a2 2 0 012-2h4zm-6 2v6h4V9H9z"></path>
                                </svg>
                            </div>
                        </div>
                        <div class="ml-4">
                            <p class="text-lg font-semibold text-gray-800">API密钥状态</p>
                            <p class="text-sm text-gray-500" x-show="keyStatus.last_updated">
                                更新于 <span x-text="keyStatus.last_updated"></span>
                            </p>
                            <p class="text-sm text-gray-400" x-show="!keyStatus.last_updated">
                                点击"手动检测"获取最新状态
                            </p>
                        </div>
                    </div>
                    <div class="flex space-x-2">
                        <button @click="refreshKeyStatus()" :disabled="loadingKeyStatus" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-lg text-purple-700 bg-purple-100 hover:bg-purple-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 disabled:opacity-50 transition-all duration-200">
                            <svg x-show="!loadingKeyStatus" class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                            </svg>
                            <svg x-show="loadingKeyStatus" class="animate-spin w-4 h-4 mr-2" fill="none" viewBox="0 0 24 24">
                                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                            </svg>
                            <span x-show="!loadingKeyStatus">刷新状态</span>
                            <span x-show="loadingKeyStatus">刷新中...</span>
                        </button>
                        <button @click="loadPersistedValidationStatus()" :disabled="loadingValidation" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-lg text-blue-700 bg-blue-100 hover:bg-blue-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 transition-all duration-200">
                            <svg x-show="!loadingValidation" class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                            <svg x-show="loadingValidation" class="animate-spin w-4 h-4 mr-2" fill="none" viewBox="0 0 24 24">
                                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                            </svg>
                            <span x-show="!loadingValidation">检查所有密钥</span>
                            <span x-show="loadingValidation">检查中...</span>
                        </button>
                    </div>
                </div>

                <!-- 密钥统计 -->
                <div class="grid grid-cols-3 gap-8">
                    <div class="text-center">
                        <div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-3">
                            <div class="w-6 h-6 bg-green-500 rounded-full"></div>
                        </div>
                        <div class="text-2xl font-bold text-green-600 mb-1" x-text="keyStatus.total_valid || 0">0</div>
                        <div class="text-sm text-gray-500">有效密钥</div>
                    </div>
                    <div class="text-center">
                        <div class="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-3">
                            <div class="w-6 h-6 bg-red-500 rounded-full"></div>
                        </div>
                        <div class="text-2xl font-bold text-red-600 mb-1" x-text="keyStatus.total_invalid || 0">0</div>
                        <div class="text-sm text-gray-500">无效密钥</div>
                    </div>
                    <div class="text-center">
                        <div class="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-3">
                            <div class="w-6 h-6 bg-gray-500 rounded-full"></div>
                        </div>
                        <div class="text-2xl font-bold text-gray-600 mb-1" x-text="keyStatus.total_keys || 0">0</div>
                        <div class="text-sm text-gray-500">总计</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Provider Groups Status -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-8">
            <div class="flex justify-between items-center mb-6">
                <h2 class="text-xl font-bold">提供商分组状态</h2>
                <div class="flex space-x-2">
                    <button @click="openCreateGroupModal()" class="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg transition duration-200 flex items-center">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                        </svg>
                        添加分组
                    </button>
                    <button @click="refreshProviderHealth()" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg transition duration-200">
                        刷新健康状态
                    </button>
                </div>
            </div>

            <!-- 密钥状态图例 -->
            <div class="mb-4 p-3 bg-gray-50 rounded-lg">
                <div class="flex items-center justify-between">
                    <span class="text-sm font-medium text-gray-700">密钥状态图例：</span>
                    <div class="flex items-center space-x-6">
                        <div class="flex items-center space-x-2">
                            <div class="w-2 h-2 bg-green-500 rounded-full"></div>
                            <span class="text-xs text-gray-600">有效</span>
                        </div>
                        <div class="flex items-center space-x-2">
                            <div class="w-2 h-2 bg-red-500 rounded-full"></div>
                            <span class="text-xs text-gray-600">无效</span>
                        </div>
                        <div class="flex items-center space-x-2">
                            <div class="w-2 h-2 bg-gray-400 rounded-full"></div>
                            <span class="text-xs text-gray-600">未检测</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 搜索和筛选 -->
            <div class="mb-6 flex flex-col sm:flex-row gap-4">
                <div class="flex-1">
                    <div class="relative">
                        <input type="text" x-model="providerSearchQuery" @input="filterProviders()" placeholder="搜索分组名称或提供商类型..." class="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <svg class="absolute left-3 top-2.5 h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                        </svg>
                    </div>
                </div>
                <div class="flex space-x-2">
                    <select x-model="providerStatusFilter" @change="filterProviders()" class="px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option value="">所有状态</option>
                        <option value="healthy">健康</option>
                        <option value="unhealthy">异常</option>
                    </select>
                    <select x-model="providerTypeFilter" @change="filterProviders()" class="px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option value="">所有类型</option>
                        <option value="openai">OpenAI</option>
                        <option value="azure_openai">Azure OpenAI</option>
                        <option value="anthropic">Anthropic</option>
                        <option value="gemini">Google Gemini</option>
                        <option value="openrouter">OpenRouter</option>
                    </select>
                </div>
            </div>

            <!-- 提供商列表 -->
            <div class="space-y-4">
                <template x-for="(provider, groupId) in paginatedProviders" :key="groupId">
                    <div class="border rounded-lg p-4 hover:shadow-md transition-shadow" :class="provider.healthy ? 'border-green-200 bg-green-50' : 'border-red-200 bg-red-50'">
                        <div class="flex items-center justify-between">
                            <!-- 左侧信息 -->
                            <div class="flex items-center space-x-4">
                                <div class="flex items-center">
                                    <div class="w-3 h-3 rounded-full mr-2" :class="provider.healthy ? 'bg-green-500' : 'bg-red-500'"></div>
                                    <div>
                                        <h3 class="font-semibold text-gray-900" x-text="provider.group_name"></h3>
                                        <p class="text-sm text-gray-500" x-text="groupId"></p>
                                    </div>
                                </div>

                                <span class="text-xs px-2 py-1 rounded-full" :class="getProviderTypeClass(provider.provider_type)" x-text="provider.provider_type.toUpperCase()"></span>

                                <!-- 状态信息 -->
                                <div class="flex items-center space-x-4 text-sm">
                                    <div class="flex items-center space-x-1">
                                        <span class="text-gray-600">健康:</span>
                                        <span :class="provider.healthy ? 'text-green-600' : 'text-red-600'" x-text="provider.healthy ? '健康' : '异常'"></span>
                                    </div>
                                    <div class="flex items-center space-x-1">
                                        <span class="text-gray-600">状态:</span>
                                        <span :class="provider.enabled !== false ? 'text-green-600' : 'text-gray-500'" x-text="provider.enabled !== false ? '启用' : '禁用'"></span>
                                    </div>
                                    <div class="flex items-center space-x-1">
                                        <span class="text-gray-600">密钥:</span>
                                        <div class="flex items-center space-x-2">
                                            <!-- 总计 -->
                                            <span class="text-gray-900 font-medium" x-text="getKeyStatusData(groupId, provider).total"></span>

                                            <!-- 有效密钥 -->
                                            <div class="flex items-center space-x-1">
                                                <div class="w-2 h-2 bg-green-500 rounded-full"></div>
                                                <span class="text-green-600 text-xs font-medium" x-text="getKeyStatusData(groupId, provider).valid"></span>
                                            </div>

                                            <!-- 无效密钥 -->
                                            <div class="flex items-center space-x-1" x-show="getKeyStatusData(groupId, provider).invalid > 0">
                                                <div class="w-2 h-2 bg-red-500 rounded-full"></div>
                                                <span class="text-red-600 text-xs font-medium" x-text="getKeyStatusData(groupId, provider).invalid"></span>
                                            </div>

                                            <!-- 未检测 -->
                                            <div class="flex items-center space-x-1" x-show="getKeyStatusData(groupId, provider).unknown > 0">
                                                <div class="w-2 h-2 bg-gray-400 rounded-full"></div>
                                                <span class="text-gray-500 text-xs font-medium" x-text="getKeyStatusData(groupId, provider).unknown"></span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="flex items-center space-x-1">
                                        <span class="text-gray-600">响应:</span>
                                        <span class="text-gray-900" x-text="formatResponseTime(provider.response_time)"></span>
                                    </div>
                                </div>
                            </div>

                            <!-- 右侧操作 -->
                            <div class="flex items-center space-x-2">
                                <button @click="validateGroupKeys(groupId, provider)" :disabled="validatingGroups[groupId]" class="inline-flex items-center px-3 py-1 border border-purple-300 text-sm font-medium rounded text-purple-700 bg-purple-50 hover:bg-purple-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 disabled:opacity-50 transition-colors">
                                    <svg x-show="!validatingGroups[groupId]" class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                    </svg>
                                    <svg x-show="validatingGroups[groupId]" class="animate-spin w-4 h-4 mr-1" fill="none" viewBox="0 0 24 24">
                                        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                        <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                    </svg>
                                    <span x-show="!validatingGroups[groupId]">检测</span>
                                    <span x-show="validatingGroups[groupId]">检测中</span>
                                </button>
                                <button @click="editGroup(groupId, provider)" class="inline-flex items-center px-3 py-1 border border-transparent text-sm font-medium rounded text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                                    <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                                    </svg>
                                    编辑
                                </button>
                                <button @click="toggleGroup(groupId, provider)" class="inline-flex items-center px-3 py-1 text-sm font-medium rounded" :class="provider.enabled !== false ? 'bg-yellow-500 hover:bg-yellow-600 text-white' : 'bg-green-500 hover:bg-green-600 text-white'">
                                    <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path x-show="provider.enabled !== false" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 9v6m4-6v6m7-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                        <path x-show="provider.enabled === false" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1m4 0h1m-6 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                    </svg>
                                    <span x-text="provider.enabled !== false ? '禁用' : '启用'"></span>
                                </button>
                                <button @click="deleteGroup(groupId, provider)" class="inline-flex items-center px-3 py-1 border border-transparent text-sm font-medium rounded text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500">
                                    <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                    </svg>
                                    删除
                                </button>
                            </div>
                        </div>

                        <!-- 错误信息 -->
                        <div x-show="provider.last_error" class="mt-3 p-2 bg-red-100 border border-red-200 rounded text-sm text-red-700">
                            <strong>错误:</strong> <span x-text="provider.last_error"></span>
                        </div>
                    </div>
                </template>

                <!-- 空状态 -->
                <div x-show="Object.keys(filteredProviders).length === 0" class="text-center py-8">
                    <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.172 16.172a4 4 0 015.656 0M9 12h6m-6-4h6m2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    <h3 class="mt-2 text-sm font-medium text-gray-900">没有找到匹配的提供商</h3>
                    <p class="mt-1 text-sm text-gray-500">尝试调整搜索条件或筛选器</p>
                </div>
            </div>

            <!-- 分页控制 -->
            <div x-show="totalProviderPages > 1" class="mt-6 flex items-center justify-between">
                <div class="text-sm text-gray-700">
                    显示 <span x-text="providerPageOffset + 1"></span>-<span x-text="Math.min(providerPageOffset + providersPerPage, Object.keys(filteredProviders).length)"></span>
                    共 <span x-text="Object.keys(filteredProviders).length"></span> 个分组
                </div>
                <div class="flex space-x-1">
                    <button @click="providerPage = Math.max(1, providerPage - 1)" :disabled="providerPage <= 1" class="px-3 py-2 text-sm border rounded disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50">
                        上一页
                    </button>
                    <span class="px-3 py-2 text-sm border-t border-b" x-text="providerPage + ' / ' + totalProviderPages"></span>
                    <button @click="providerPage = Math.min(totalProviderPages, providerPage + 1)" :disabled="providerPage >= totalProviderPages" class="px-3 py-2 text-sm border rounded disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50">
                        下一页
                    </button>
                </div>
            </div>
        </div>

        <!-- Provider Models -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-8">
            <div class="flex justify-between items-center mb-6">
                <h2 class="text-xl font-bold">支持的模型</h2>
                <div class="flex space-x-2">
                    <select x-model="selectedProvider" @change="loadProviderModels()" class="px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option value="">所有提供商</option>
                        <template x-for="(provider, groupId) in providerStatuses" :key="groupId">
                            <option :value="groupId" x-text="provider.group_name"></option>
                        </template>
                    </select>
                    <button @click="loadProviderModels()" class="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg transition duration-200">
                        加载模型
                    </button>
                </div>
            </div>

            <div x-show="loadingModels" class="text-center py-8">
                <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
                <p class="mt-2 text-gray-600">加载模型中...</p>
            </div>

            <div x-show="!loadingModels && Object.keys(providerModels).length > 0" class="space-y-4">
                <template x-for="(models, groupId) in providerModels" :key="groupId">
                    <div class="border rounded-lg p-4">
                        <h3 class="font-semibold text-gray-900 mb-3" x-text="models.group_name + ' (' + models.provider_type + ')'"></h3>
                        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-2">
                            <template x-for="model in (models.models && models.models.data ? models.models.data : [])" :key="model.id">
                                <div class="bg-gray-100 px-3 py-2 rounded text-sm">
                                    <span class="font-mono" x-text="model.id"></span>
                                    <span x-show="model.owned_by" class="text-gray-500 ml-2" x-text="'(' + model.owned_by + ')'"></span>
                                </div>
                            </template>
                        </div>
                    </div>
                </template>
            </div>

            <div x-show="!loadingModels && Object.keys(providerModels).length === 0" class="text-center py-8 text-gray-500">
                <p>暂无模型数据，请选择提供商并点击加载模型</p>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="bg-white rounded-lg shadow-md p-6">
            <h2 class="text-xl font-bold mb-6">快速操作</h2>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <button @click="showProxyKeyModal = true" class="bg-indigo-500 hover:bg-indigo-600 text-white px-4 py-2 rounded-lg transition duration-200">
                    代理密钥管理
                </button>
                <button @click="exportHealthReport()" class="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg transition duration-200">
                    导出健康报告
                </button>
                <button @click="viewSystemLogs()" class="bg-purple-500 hover:bg-purple-600 text-white px-4 py-2 rounded-lg transition duration-200">
                    查看请求日志
                </button>
            </div>
            <div class="mt-4 text-sm text-gray-500">
                最后更新: <span x-text="formatDate(lastUpdate)"></span>
            </div>
        </div>

        <!-- Group Management Modal -->
        <div x-show="showCreateGroupModal || showEditGroupModal" x-cloak class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4" @click.self="closeGroupModal()"
             x-transition:enter="transition ease-out duration-300"
             x-transition:enter-start="opacity-0"
             x-transition:enter-end="opacity-100"
             x-transition:leave="transition ease-in duration-200"
             x-transition:leave-start="opacity-100"
             x-transition:leave-end="opacity-0">
            <div class="bg-white rounded-lg shadow-2xl w-full max-w-4xl max-h-[90vh] flex flex-col">
                <!-- 头部 -->
                <div class="flex justify-between items-center p-6 border-b border-gray-200">
                    <div class="flex items-center space-x-3">
                        <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                            <svg class="w-4 h-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
                            </svg>
                        </div>
                        <div>
                            <h3 class="text-xl font-bold text-gray-900" x-text="showCreateGroupModal ? '添加提供商分组' : '编辑提供商分组'"></h3>
                            <p class="text-sm text-gray-500" x-text="showCreateGroupModal ? '配置新的API提供商分组' : '修改现有提供商分组配置'"></p>
                        </div>
                    </div>
                    <button @click="closeGroupModal()" class="text-gray-400 hover:text-gray-600 transition-colors">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>

                <!-- 内容区域 -->
                <div class="flex-1 overflow-y-auto p-6">
                    <form id="groupForm" @submit.prevent="submitGroupForm()" class="space-y-8">
                        <!-- 基本信息区域 -->
                        <div class="bg-gray-50 rounded-lg p-6">
                            <div class="flex items-center space-x-2 mb-4">
                                <svg class="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                                <h4 class="text-lg font-semibold text-gray-900">基本信息</h4>
                            </div>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">分组ID *</label>
                                    <input type="text" x-model="groupFormData.group_id" :disabled="showEditGroupModal" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent" required placeholder="如: openai-group">
                                    <p class="text-xs text-gray-500 mt-1">唯一标识符，创建后不可修改</p>
                                </div>

                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">分组名称 *</label>
                                    <input type="text" x-model="groupFormData.name" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent" required placeholder="如: OpenAI 官方">
                                    <p class="text-xs text-gray-500 mt-1">用于显示的友好名称</p>
                                </div>
                            </div>
                        </div>

                        <!-- 提供商配置区域 -->
                        <div class="bg-gray-50 rounded-lg p-6">
                            <div class="flex items-center space-x-2 mb-4">
                                <svg class="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                                <h4 class="text-lg font-semibold text-gray-900">提供商配置</h4>
                            </div>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">提供商类型 *</label>
                                    <select x-model="groupFormData.provider_type" @change="onProviderTypeChange()" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent" required>
                                        <option value="">请选择提供商类型</option>
                                        <option value="openai">OpenAI</option>
                                        <option value="azure_openai">Azure OpenAI</option>
                                        <option value="anthropic">Anthropic</option>
                                        <option value="gemini">Google Gemini</option>
                                        <option value="openrouter">OpenRouter</option>
                                    </select>
                                    <p class="text-xs text-gray-500 mt-1">选择API提供商类型</p>
                                </div>

                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">Base URL *</label>
                                    <input type="url" x-model="groupFormData.base_url" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent" required placeholder="如: https://api.openai.com/v1">
                                    <p class="text-xs text-gray-500 mt-1">API服务的基础URL地址</p>
                                </div>

                                <div class="md:col-span-2">
                                    <label class="block text-sm font-medium text-gray-700 mb-2">启用状态</label>
                                    <label class="flex items-center space-x-2 cursor-pointer">
                                        <input type="checkbox" x-model="groupFormData.enabled" class="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500">
                                        <span class="text-sm text-gray-700">启用此分组</span>
                                    </label>
                                    <p class="text-xs text-gray-500 mt-1">禁用后该分组将不会处理请求</p>
                                </div>
                            </div>
                        </div>

                        <!-- 配置参数区域 -->
                        <div class="bg-gray-50 rounded-lg p-6">
                            <div class="flex items-center space-x-2 mb-6">
                                <svg class="w-5 h-5 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4"></path>
                                </svg>
                                <h4 class="text-lg font-semibold text-gray-900">配置参数</h4>
                            </div>
                            <!-- 基础配置 -->
                            <div class="mb-6">
                                <h5 class="text-sm font-medium text-gray-800 mb-4 flex items-center">
                                    <svg class="w-4 h-4 mr-2 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                    </svg>
                                    基础配置
                                </h5>
                                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                                    <div class="bg-white rounded-lg p-4 border border-gray-200 shadow-sm">
                                        <label class="block text-sm font-medium text-gray-700 mb-2">
                                            <svg class="w-4 h-4 inline mr-1 text-orange-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                            </svg>
                                            超时时间（秒）
                                        </label>
                                        <input type="number" x-model="groupFormData.timeout" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent" min="1" max="600" placeholder="30">
                                        <p class="text-xs text-gray-500 mt-2">请求超时时间，建议30-120秒</p>
                                    </div>

                                    <div class="bg-white rounded-lg p-4 border border-gray-200 shadow-sm">
                                        <label class="block text-sm font-medium text-gray-700 mb-2">
                                            <svg class="w-4 h-4 inline mr-1 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                                            </svg>
                                            最大重试次数
                                        </label>
                                        <input type="number" x-model="groupFormData.max_retries" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent" min="0" max="10" placeholder="3">
                                        <p class="text-xs text-gray-500 mt-2">失败后的重试次数，建议1-5次</p>
                                    </div>

                                    <div class="bg-white rounded-lg p-4 border border-gray-200 shadow-sm">
                                        <label class="block text-sm font-medium text-gray-700 mb-2">
                                            <svg class="w-4 h-4 inline mr-1 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                                            </svg>
                                            轮换策略
                                        </label>
                                        <select x-model="groupFormData.rotation_strategy" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                            <option value="round_robin">轮询 (Round Robin)</option>
                                            <option value="random">随机 (Random)</option>
                                            <option value="least_used">最少使用 (Least Used)</option>
                                        </select>
                                        <p class="text-xs text-gray-500 mt-2">多个密钥时的选择策略</p>
                                    </div>
                                </div>
                            </div>

                            <!-- RPM限制配置 -->
                            <div class="mb-6">
                                <h5 class="text-sm font-medium text-gray-800 mb-4 flex items-center">
                                    <svg class="w-4 h-4 mr-2 text-yellow-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                                    </svg>
                                    RPM限制
                                </h5>
                                <div class="bg-white rounded-lg p-4 border border-gray-200 shadow-sm">
                                    <label class="block text-sm font-medium text-gray-700 mb-2">每分钟请求数限制</label>
                                    <input type="number" x-model="groupFormData.rpm_limit" min="0" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent" placeholder="0">
                                    <p class="text-xs text-gray-500 mt-2">每分钟请求数限制，0表示无限制</p>
                                </div>
                            </div>

                            <!-- 高级选项 -->
                            <div>
                                <h5 class="text-sm font-medium text-gray-800 mb-4 flex items-center">
                                    <svg class="w-4 h-4 mr-2 text-purple-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                    </svg>
                                    高级选项
                                </h5>
                                <div class="bg-white rounded-lg p-4 border border-gray-200 shadow-sm">
                                    <label class="flex items-start space-x-3 cursor-pointer">
                                        <input type="checkbox" x-model="groupFormData.use_native_response" class="mt-1 rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50">
                                        <div>
                                            <span class="text-sm font-medium text-gray-700">使用原生接口响应</span>
                                            <p class="text-xs text-gray-500 mt-1">勾选后将返回提供商的原生响应格式，而不是OpenAI兼容格式</p>
                                        </div>
                                    </label>
                                </div>
                            </div>
                        </div>

                        <!-- API密钥区域 -->
                        <div class="bg-gray-50 rounded-lg p-6">
                            <div class="flex items-center justify-between mb-4">
                                <div class="flex items-center space-x-2">
                                    <svg class="w-5 h-5 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 7a2 2 0 012 2m0 0a2 2 0 012 2v6a2 2 0 01-2 2H9a2 2 0 01-2-2V9a2 2 0 012-2m0 0V7a2 2 0 012-2h4zm-6 2v6h4V9H9z"></path>
                                    </svg>
                                    <h4 class="text-lg font-semibold text-gray-900">API密钥管理</h4>
                                </div>
                                <div class="flex flex-wrap gap-2">
                                    <button type="button" @click="showBatchAddModal = true" class="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors">
                                        <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                                        </svg>
                                        批量添加
                                    </button>
                                    <button type="button" @click="addGroupApiKey()" class="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 transition-colors">
                                        <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                                        </svg>
                                        添加密钥
                                    </button>
                                    <button type="button" @click="validateKeys()" :disabled="validatingKeys || groupFormData.api_keys.filter(k => k.trim()).length === 0" class="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-purple-600 hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors">
                                        <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                        </svg>
                                        <span x-show="!validatingKeys">验证密钥</span>
                                        <span x-show="validatingKeys">验证中...</span>
                                    </button>
                                    <button type="button" @click="deleteInvalidKeys()" x-show="getInvalidKeyCount() > 0" class="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 transition-colors">
                                        <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                        </svg>
                                        删除失效密钥 (<span x-text="getInvalidKeyCount()"></span>)
                                    </button>
                                    <button type="button" @click="deleteSelectedKeys()" x-show="selectedKeys.length > 0" class="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 transition-colors">
                                        <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                        </svg>
                                        删除选中 (<span x-text="selectedKeys.length"></span>)
                                    </button>

                                </div>
                            </div>

                            <!-- 密钥列表 -->
                            <div class="border rounded-md max-h-60 overflow-y-auto">
                                <div class="space-y-1 p-2">
                                    <template x-for="(key, index) in paginatedKeys" :key="index + keyPageOffset">
                                        <div class="flex items-center space-x-2 p-2 hover:bg-gray-50 rounded" :class="getKeyValidationClass(index + keyPageOffset)">
                                            <input type="checkbox" :value="index + keyPageOffset" x-model="selectedKeys" class="rounded">
                                            <div class="flex-1 relative">
                                                <input type="text" x-model="groupFormData.api_keys[index + keyPageOffset]" class="w-full px-2 py-1 border border-gray-300 rounded text-sm focus:outline-none focus:ring-1 focus:ring-blue-500" placeholder="输入API密钥">
                                                <!-- 验证状态指示器 -->
                                                <div class="absolute right-2 top-1/2 transform -translate-y-1/2 flex items-center space-x-1">
                                                    <!-- 有效状态 -->
                                                    <div x-show="keyValidationStatus[index + keyPageOffset] === 'valid'" class="flex items-center" title="密钥有效">
                                                        <svg class="w-4 h-4 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                                                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                                                        </svg>
                                                    </div>
                                                    <!-- 无效状态 -->
                                                    <div x-show="keyValidationStatus[index + keyPageOffset] === 'invalid'" class="flex items-center" title="密钥无效">
                                                        <svg class="w-4 h-4 text-red-500" fill="currentColor" viewBox="0 0 20 20">
                                                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"></path>
                                                        </svg>
                                                    </div>
                                                    <!-- 验证中状态 -->
                                                    <div x-show="keyValidationStatus[index + keyPageOffset] === 'validating'" class="flex items-center" title="验证中">
                                                        <svg class="w-4 h-4 text-yellow-500 animate-spin" fill="none" viewBox="0 0 24 24">
                                                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                                        </svg>
                                                    </div>
                                                </div>
                                            </div>
                                            <button type="button" @click="removeGroupApiKey(index + keyPageOffset)" class="text-red-500 hover:text-red-700 p-1">
                                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                                </svg>
                                            </button>
                                        </div>
                                    </template>
                                </div>

                                <!-- 分页控制 -->
                                <div x-show="groupFormData.api_keys.length > keysPerPage" class="border-t p-2 flex justify-between items-center bg-gray-50">
                                    <div class="text-sm text-gray-600">
                                        显示 <span x-text="keyPageOffset + 1"></span>-<span x-text="Math.min(keyPageOffset + keysPerPage, groupFormData.api_keys.length)"></span>
                                        共 <span x-text="groupFormData.api_keys.length"></span> 个密钥
                                    </div>
                                    <div class="flex space-x-1">
                                        <button type="button" @click="keyPage = Math.max(1, keyPage - 1)" :disabled="keyPage <= 1" class="px-2 py-1 text-sm border rounded disabled:opacity-50">
                                            上一页
                                        </button>
                                        <button type="button" @click="keyPage = Math.min(totalKeyPages, keyPage + 1)" :disabled="keyPage >= totalKeyPages" class="px-2 py-1 text-sm border rounded disabled:opacity-50">
                                            下一页
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 支持的模型 -->
                        <div class="md:col-span-2 mt-4">
                            <div class="flex justify-between items-center mb-2">
                                <h4 class="font-semibold">支持的模型（可选）</h4>
                                <div class="flex space-x-2">
                                    <button type="button" @click="loadAvailableModels()" :disabled="!groupFormData.provider_type || !groupFormData.base_url || groupFormData.api_keys.filter(k => k.trim()).length === 0" class="bg-blue-500 hover:bg-blue-600 text-white px-3 py-1 rounded text-sm disabled:opacity-50">
                                        <span x-show="!loadingModels">加载模型</span>
                                        <span x-show="loadingModels">加载中...</span>
                                    </button>
                                    <button type="button" @click="selectAllModels()" x-show="availableModels.length > 0" class="bg-green-500 hover:bg-green-600 text-white px-3 py-1 rounded text-sm">
                                        全选
                                    </button>
                                    <button type="button" @click="clearAllModels()" x-show="groupFormData.models.length > 0" class="bg-gray-500 hover:bg-gray-600 text-white px-3 py-1 rounded text-sm">
                                        清空
                                    </button>
                                </div>
                            </div>

                            <!-- 模型选择器 -->
                            <div x-show="availableModels.length > 0" class="border rounded-md p-3 bg-gray-50 mb-3">
                                <div class="flex justify-between items-center mb-2">
                                    <div class="text-sm text-gray-600">从API加载的可用模型：</div>
                                    <div class="text-xs text-gray-500">
                                        显示 <span x-text="filteredModels.length"></span> / <span x-text="availableModels.length"></span> 个模型
                                    </div>
                                </div>

                                <!-- 搜索框 -->
                                <div class="mb-3">
                                    <div class="relative">
                                        <input type="text" x-model="modelSearchQuery" @input="filterModels()" placeholder="搜索模型名称..." class="w-full px-3 py-2 pl-8 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                        <svg class="absolute left-2 top-2.5 h-4 w-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                                        </svg>
                                        <button x-show="modelSearchQuery" @click="clearModelSearch()" class="absolute right-2 top-2 text-gray-400 hover:text-gray-600">
                                            <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                            </svg>
                                        </button>
                                    </div>
                                </div>

                                <!-- 模型列表 -->
                                <div class="max-h-40 overflow-y-auto space-y-1">
                                    <template x-for="model in filteredModels" :key="model.id">
                                        <label class="flex items-center space-x-2 text-sm hover:bg-white p-1 rounded">
                                            <input type="checkbox" :value="model.id" x-model="groupFormData.models" class="rounded">
                                            <span class="font-mono" x-text="model.id"></span>
                                            <span x-show="model.owned_by" class="text-gray-500" x-text="'(' + model.owned_by + ')'"></span>
                                        </label>
                                    </template>
                                    <div x-show="filteredModels.length === 0 && modelSearchQuery" class="text-center py-4 text-gray-500 text-sm">
                                        未找到匹配的模型
                                    </div>
                                </div>
                            </div>

                            <!-- 手动输入模型 -->
                            <div>
                                <div class="text-sm text-gray-600 mb-2">或手动输入模型名称：</div>
                                <textarea x-model="modelsText" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" rows="3" placeholder="每行一个模型名称，留空表示支持所有模型"></textarea>
                                <p class="text-sm text-gray-500 mt-1">每行输入一个模型名称，如：gpt-3.5-turbo</p>
                            </div>

                            <!-- 已选择的模型预览 -->
                            <div x-show="groupFormData.models.length > 0" class="mt-3">
                                <div class="text-sm text-gray-600 mb-2">已选择的模型 (<span x-text="groupFormData.models.length"></span>)：</div>
                                <div class="flex flex-wrap gap-1">
                                    <template x-for="model in groupFormData.models" :key="model">
                                        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs bg-blue-100 text-blue-800">
                                            <span x-text="model"></span>
                                            <button type="button" @click="removeModel(model)" class="ml-1 text-blue-600 hover:text-blue-800">
                                                <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                                </svg>
                                            </button>
                                        </span>
                                    </template>
                                </div>
                            </div>
                        </div>

                        <!-- 模型重命名配置 -->
                        <div class="md:col-span-2 mt-4">
                            <div class="flex justify-between items-center mb-2">
                                <h4 class="font-semibold">模型重命名配置（可选）</h4>
                                <button type="button" @click="showModelMappingHelp = !showModelMappingHelp" class="text-gray-400 hover:text-gray-600">
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                    </svg>
                                </button>
                            </div>

                            <!-- 帮助信息 -->
                            <div x-show="showModelMappingHelp" x-collapse class="mb-3 p-3 bg-blue-50 border border-blue-200 rounded-md text-sm text-blue-700">
                                <p class="mb-2"><strong>模型重命名功能说明：</strong></p>
                                <ul class="list-disc list-inside space-y-1">
                                    <li>可以为该分组的模型设置别名，统一不同分组的模型名称</li>
                                    <li>客户端使用别名请求时，系统会自动转换为实际的模型名称</li>
                                    <li>例如：将"gpt-4-turbo"重命名为"gpt-4"，客户端可以统一使用"gpt-4"</li>
                                    <li>支持多个别名映射到同一个实际模型</li>
                                </ul>
                            </div>

                            <!-- 模型映射列表 -->
                            <div class="space-y-2">
                                <template x-for="(mapping, index) in modelMappings" :key="index">
                                    <div class="flex items-center space-x-2 p-2 border border-gray-200 rounded-md">
                                        <div class="flex-1">
                                            <label class="block text-xs text-gray-500 mb-1">别名（客户端使用）</label>
                                            <input type="text"
                                                   x-model="mapping.alias"
                                                   class="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500"
                                                   placeholder="例如: gpt-4">
                                        </div>
                                        <div class="text-gray-400">→</div>
                                        <div class="flex-1">
                                            <label class="block text-xs text-gray-500 mb-1">实际模型名称</label>
                                            <select x-model="mapping.original"
                                                    class="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500">
                                                <option value="">请选择模型</option>
                                                <template x-for="model in groupFormData.models" :key="model">
                                                    <option :value="model" :selected="model === mapping.original" x-text="model"></option>
                                                </template>
                                            </select>
                                        </div>
                                        <button type="button"
                                                @click="removeModelMapping(index)"
                                                class="text-red-500 hover:text-red-700 p-1">
                                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                            </svg>
                                        </button>
                                    </div>
                                </template>

                                <!-- 添加新映射按钮 -->
                                <button type="button"
                                        @click="addModelMapping()"
                                        class="w-full py-2 px-4 border-2 border-dashed border-gray-300 rounded-md text-gray-500 hover:border-gray-400 hover:text-gray-600 transition-colors">
                                    <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
                                    </svg>
                                    添加模型重命名
                                </button>
                            </div>
                        </div>

                        <!-- JSON请求参数覆盖 -->
                        <div class="md:col-span-2 mt-4">
                            <div class="flex justify-between items-center mb-2">
                                <h4 class="font-semibold">JSON请求参数覆盖（可选）</h4>
                                <button type="button" @click="showRequestParamsHelp = !showRequestParamsHelp" class="text-gray-400 hover:text-gray-600">
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                    </svg>
                                </button>
                            </div>

                            <!-- 帮助信息 -->
                            <div x-show="showRequestParamsHelp" x-collapse class="mb-3 p-3 bg-blue-50 border border-blue-200 rounded-md text-sm text-blue-700">
                                <p class="mb-2"><strong>JSON请求参数覆盖功能说明：</strong></p>
                                <ul class="list-disc list-inside space-y-1">
                                    <li>可以为该分组设置默认的请求参数，这些参数会覆盖客户端发送的参数</li>
                                    <li>支持的参数：temperature（温度）、max_tokens（最大token数）、top_p、stop（停止词）</li>
                                    <li>示例：{"temperature": 0.7, "max_tokens": 1000, "top_p": 0.9}</li>
                                    <li>留空表示不覆盖任何参数，使用客户端发送的原始参数</li>
                                </ul>
                            </div>

                            <!-- JSON编辑器 -->
                            <div>
                                <textarea x-model="requestParamsText"
                                          @input="validateRequestParams()"
                                          class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 font-mono text-sm"
                                          rows="6"
                                          placeholder='{"temperature": 0.7, "max_tokens": 1000, "top_p": 0.9, "stop": ["Human:", "AI:"]}'></textarea>
                                <p class="text-sm text-gray-500 mt-1">请输入有效的JSON格式，留空表示不覆盖任何参数</p>
                            </div>

                            <!-- JSON验证状态 -->
                            <div x-show="requestParamsValidationMessage" class="mt-2">
                                <div :class="requestParamsValidationError ? 'text-red-600' : 'text-green-600'" class="text-sm flex items-center">
                                    <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path x-show="requestParamsValidationError" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                        <path x-show="!requestParamsValidationError" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                    </svg>
                                    <span x-text="requestParamsValidationMessage"></span>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>

                <!-- 底部操作 -->
                <div class="border-t border-gray-200 px-6 py-4 bg-gray-50 rounded-b-lg">
                    <div class="flex justify-between items-center">
                        <div class="text-sm text-gray-500">
                            <span x-show="showCreateGroupModal">填写完整信息后点击创建</span>
                            <span x-show="showEditGroupModal">修改完成后点击更新保存</span>
                        </div>
                        <div class="flex space-x-3">
                            <button type="button" @click="closeGroupModal()" class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors">
                                取消
                            </button>
                            <button type="submit" form="groupForm" :disabled="submittingGroup" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors">
                                <svg x-show="submittingGroup" class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" fill="none" viewBox="0 0 24 24">
                                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                </svg>
                                <span x-show="!submittingGroup" x-text="showCreateGroupModal ? '创建分组' : '更新分组'"></span>
                                <span x-show="submittingGroup">处理中...</span>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Batch Add Keys Modal -->
        <div x-show="showBatchAddModal" x-cloak
             x-transition:enter="transition ease-out duration-300"
             x-transition:enter-start="opacity-0"
             x-transition:enter-end="opacity-100"
             x-transition:leave="transition ease-in duration-200"
             x-transition:leave-start="opacity-100"
             x-transition:leave-end="opacity-0"
             class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"
             @click.self="showBatchAddModal = false">
            <div class="bg-white rounded-lg p-6 w-full max-w-lg">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-lg font-bold">批量添加API密钥</h3>
                    <button @click="showBatchAddModal = false" class="text-gray-500 hover:text-gray-700">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>

                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 mb-2">API密钥列表</label>
                    <textarea x-model="batchKeysText" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" rows="8" placeholder="每行输入一个API密钥&#10;支持以下格式：&#10;sk-1234567890abcdef&#10;your-api-key-here&#10;&#10;空行将被忽略"></textarea>
                    <p class="text-sm text-gray-500 mt-1">每行输入一个API密钥，空行将被自动忽略</p>
                </div>

                <div class="flex justify-end space-x-4">
                    <button type="button" @click="showBatchAddModal = false" class="px-4 py-2 border border-gray-300 rounded-md hover:bg-gray-50">
                        取消
                    </button>
                    <button type="button" @click="addBatchKeys()" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-md">
                        添加密钥
                    </button>
                </div>
            </div>
        </div>

        <!-- Proxy Key Management Modal -->
        <div x-show="showProxyKeyModal" x-cloak class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4" @click.self="showProxyKeyModal = false"
             x-transition:enter="transition ease-out duration-300"
             x-transition:enter-start="opacity-0"
             x-transition:enter-end="opacity-100"
             x-transition:leave="transition ease-in duration-200"
             x-transition:leave-start="opacity-100"
             x-transition:leave-end="opacity-0">
            <div class="bg-white rounded-lg shadow-2xl w-full max-w-6xl max-h-[90vh] flex flex-col">
                <!-- 头部 -->
                <div class="flex justify-between items-center p-6 border-b border-gray-200">
                    <div class="flex items-center space-x-3">
                        <div class="w-8 h-8 bg-indigo-100 rounded-full flex items-center justify-center">
                            <svg class="w-4 h-4 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 7a2 2 0 012 2m0 0a2 2 0 012 2v6a2 2 0 01-2 2H9a2 2 0 01-2-2V9a2 2 0 012-2m0 0a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2"></path>
                            </svg>
                        </div>
                        <div>
                            <h3 class="text-xl font-bold text-gray-900">代理服务 API 密钥</h3>
                            <p class="text-sm text-gray-500">管理用于访问代理服务的API密钥</p>
                        </div>
                    </div>
                    <button @click="showProxyKeyModal = false" class="text-gray-400 hover:text-gray-600 transition-colors">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>

                <!-- 内容 -->
                <div class="flex-1 overflow-y-auto p-6">
                    <!-- 操作栏 -->
                    <div class="mb-6 flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
                        <!-- 生成新密钥按钮 -->
                        <button @click="showGenerateProxyKeyForm = true" class="bg-indigo-500 hover:bg-indigo-600 text-white px-4 py-2 rounded-lg transition duration-200">
                            <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
                            </svg>
                            生成新密钥
                        </button>

                        <!-- 搜索框和页面大小选择 -->
                        <div class="flex flex-col sm:flex-row gap-2">
                            <!-- 搜索框 -->
                            <div class="relative">
                                <input type="text"
                                       x-model="proxyKeySearch"
                                       @input="searchProxyKeys()"
                                       @keyup.enter="searchProxyKeys()"
                                       placeholder="搜索密钥名称、描述或密钥..."
                                       class="w-full sm:w-64 pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent">
                                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                    <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                                    </svg>
                                </div>
                                <button x-show="proxyKeySearch"
                                        @click="clearProxyKeySearch()"
                                        class="absolute inset-y-0 right-0 pr-3 flex items-center">
                                    <svg class="h-4 w-4 text-gray-400 hover:text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                    </svg>
                                </button>
                            </div>

                            <!-- 页面大小选择 -->
                            <select x-model="proxyKeyPageSize"
                                    @change="changeProxyKeyPageSize()"
                                    class="px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500">
                                <option value="5">5条/页</option>
                                <option value="10">10条/页</option>
                                <option value="20">20条/页</option>
                                <option value="50">50条/页</option>
                            </select>
                        </div>
                    </div>

                    <!-- 生成密钥表单 -->
                    <div x-show="showGenerateProxyKeyForm" x-cloak class="mb-6 p-4 bg-gray-50 rounded-lg">
                        <h4 class="text-lg font-medium mb-4">生成新的代理密钥</h4>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">密钥名称</label>
                                <input type="text" x-model="newProxyKey.name" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500" placeholder="输入密钥名称">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">描述</label>
                                <input type="text" x-model="newProxyKey.description" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500" placeholder="输入描述信息">
                            </div>
                        </div>
                        <div class="mt-4">
                            <label class="block text-sm font-medium text-gray-700 mb-2">允许访问的分组</label>
                            <div class="grid grid-cols-2 md:grid-cols-3 gap-2">
                                <template x-for="(provider, groupId) in providerStatuses" :key="groupId">
                                    <label class="flex items-center space-x-2">
                                        <input type="checkbox"
                                               :value="groupId"
                                               :checked="newProxyKey.allowedGroups.includes(groupId)"
                                               @change="toggleProxyKeyGroup(groupId, $event.target.checked)"
                                               class="rounded border-gray-300 text-indigo-600 focus:ring-indigo-500">
                                        <span class="text-sm" x-text="provider.group_name"></span>
                                    </label>
                                </template>
                            </div>
                            <p class="text-xs text-gray-500 mt-1">不选择任何分组表示可以访问所有分组</p>
                        </div>
                        <div class="mt-4 flex space-x-2">
                            <button @click="generateProxyKey()" class="bg-indigo-500 hover:bg-indigo-600 text-white px-4 py-2 rounded-md">
                                生成密钥
                            </button>
                            <button @click="showGenerateProxyKeyForm = false; resetProxyKeyForm()" class="bg-gray-300 hover:bg-gray-400 text-gray-700 px-4 py-2 rounded-md">
                                取消
                            </button>
                        </div>
                    </div>

                    <!-- 密钥列表 -->
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">密钥</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">名称</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">使用次数</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">创建时间</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                <template x-for="key in proxyKeys" :key="key.id">
                                    <tr>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="flex items-center">
                                                <code class="text-sm font-mono bg-gray-100 px-2 py-1 rounded" x-text="key.key.substring(0, 20) + '...'"></code>
                                                <button @click="copyToClipboard(key.key)" class="ml-2 text-gray-400 hover:text-gray-600">
                                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                                                    </svg>
                                                </button>
                                            </div>
                                            <div class="mt-1">
                                                <template x-if="key.allowed_groups && key.allowed_groups.length > 0">
                                                    <div class="flex flex-wrap gap-1">
                                                        <template x-for="groupId in key.allowed_groups" :key="groupId">
                                                            <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800" x-text="getGroupName(groupId)"></span>
                                                        </template>
                                                    </div>
                                                </template>
                                                <template x-if="!key.allowed_groups || key.allowed_groups.length === 0">
                                                    <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-green-100 text-green-800">所有分组</span>
                                                </template>
                                            </div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="text-sm font-medium text-gray-900" x-text="key.name"></div>
                                            <div class="text-sm text-gray-500" x-text="key.description"></div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900" x-text="key.usage_count || 0"></td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500" x-text="formatDate(key.created_at)"></td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                            <button @click="editProxyKey(key)" class="text-indigo-600 hover:text-indigo-900 mr-3">编辑</button>
                                            <button @click="deleteProxyKey(key.id)" class="text-red-600 hover:text-red-900">删除</button>
                                        </td>
                                    </tr>
                                </template>
                                <template x-if="proxyKeys.length === 0">
                                    <tr>
                                        <td colspan="5" class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 text-center">
                                            <template x-if="proxyKeySearch.trim()">
                                                <span>未找到匹配的代理密钥</span>
                                            </template>
                                            <template x-if="!proxyKeySearch.trim()">
                                                <span>暂无代理密钥</span>
                                            </template>
                                        </td>
                                    </tr>
                                </template>
                            </tbody>
                        </table>
                    </div>

                    <!-- 分页控件 -->
                    <div x-show="proxyKeyPagination.total > 0" class="mt-6 flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
                        <!-- 分页信息 -->
                        <div class="text-sm text-gray-700">
                            显示第 <span class="font-medium" x-text="((proxyKeyPagination.page - 1) * proxyKeyPagination.page_size + 1)"></span>
                            到 <span class="font-medium" x-text="Math.min(proxyKeyPagination.page * proxyKeyPagination.page_size, proxyKeyPagination.total)"></span>
                            条，共 <span class="font-medium" x-text="proxyKeyPagination.total"></span> 条记录
                        </div>

                        <!-- 分页按钮 -->
                        <div class="flex items-center space-x-2">
                            <!-- 上一页 -->
                            <button @click="goToProxyKeyPage(proxyKeyPagination.page - 1)"
                                    :disabled="!proxyKeyPagination.has_prev"
                                    :class="proxyKeyPagination.has_prev ? 'text-gray-500 hover:text-gray-700' : 'text-gray-300 cursor-not-allowed'"
                                    class="px-3 py-2 text-sm font-medium border border-gray-300 rounded-md">
                                上一页
                            </button>

                            <!-- 页码 -->
                            <template x-for="page in Array.from({length: Math.min(5, proxyKeyPagination.total_pages)}, (_, i) => {
                                const start = Math.max(1, proxyKeyPagination.page - 2);
                                const end = Math.min(proxyKeyPagination.total_pages, start + 4);
                                const adjustedStart = Math.max(1, end - 4);
                                return adjustedStart + i;
                            }).filter(p => p <= proxyKeyPagination.total_pages)" :key="page">
                                <button @click="goToProxyKeyPage(page)"
                                        :class="page === proxyKeyPagination.page ? 'bg-indigo-500 text-white' : 'text-gray-500 hover:text-gray-700'"
                                        class="px-3 py-2 text-sm font-medium border border-gray-300 rounded-md">
                                    <span x-text="page"></span>
                                </button>
                            </template>

                            <!-- 下一页 -->
                            <button @click="goToProxyKeyPage(proxyKeyPagination.page + 1)"
                                    :disabled="!proxyKeyPagination.has_next"
                                    :class="proxyKeyPagination.has_next ? 'text-gray-500 hover:text-gray-700' : 'text-gray-300 cursor-not-allowed'"
                                    class="px-3 py-2 text-sm font-medium border border-gray-300 rounded-md">
                                下一页
                            </button>
                        </div>
                    </div>

                    <!-- 编辑代理密钥模态框 -->
                    <div x-show="showEditProxyKeyModal" x-cloak class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
                        <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
                            <div class="mt-3">
                                <h3 class="text-lg font-medium text-gray-900 mb-4">编辑代理密钥</h3>
                                <form @submit.prevent="updateProxyKey()">
                                    <div class="mb-4">
                                        <label class="block text-sm font-medium text-gray-700 mb-2">密钥名称</label>
                                        <input type="text" x-model="editingProxyKey.name" required
                                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
                                               placeholder="输入密钥名称">
                                    </div>
                                    <div class="mb-4">
                                        <label class="block text-sm font-medium text-gray-700 mb-2">描述</label>
                                        <input type="text" x-model="editingProxyKey.description"
                                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
                                               placeholder="输入描述信息">
                                    </div>
                                    <div class="mb-4">
                                        <label class="flex items-center">
                                            <input type="checkbox" x-model="editingProxyKey.is_active"
                                                   class="rounded border-gray-300 text-indigo-600 focus:ring-indigo-500">
                                            <span class="ml-2 text-sm font-medium text-gray-700">启用密钥</span>
                                        </label>
                                    </div>
                                    <div class="mb-4">
                                        <label class="block text-sm font-medium text-gray-700 mb-2">允许访问的分组</label>
                                        <div class="max-h-40 overflow-y-auto border border-gray-300 rounded-md p-2">
                                            <template x-for="(provider, groupId) in providerStatuses" :key="groupId">
                                                <label class="flex items-center space-x-2 py-1">
                                                    <input type="checkbox"
                                                           :value="groupId"
                                                           :checked="editingProxyKey.allowedGroups.includes(groupId)"
                                                           @change="toggleEditingProxyKeyGroup(groupId, $event.target.checked)"
                                                           class="rounded border-gray-300 text-indigo-600 focus:ring-indigo-500">
                                                    <span class="text-sm" x-text="provider.group_name"></span>
                                                </label>
                                            </template>
                                        </div>
                                        <p class="text-xs text-gray-500 mt-1">不选择任何分组表示可以访问所有分组</p>
                                    </div>
                                    <div class="flex space-x-2">
                                        <button type="submit"
                                                class="bg-indigo-500 hover:bg-indigo-600 text-white px-4 py-2 rounded-md">
                                            更新密钥
                                        </button>
                                        <button type="button" @click="closeEditProxyKeyModal()"
                                                class="bg-gray-300 hover:bg-gray-400 text-gray-700 px-4 py-2 rounded-md">
                                            取消
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Success/Error Messages -->
        <div x-show="message" x-cloak x-transition class="fixed top-4 right-4 z-50">
            <div class="px-4 py-2 rounded-md text-white" :class="messageType === 'success' ? 'bg-green-500' : 'bg-red-500'">
                <span x-text="message"></span>
            </div>
        </div>
    </div>

    <script>
        function multiProviderDashboard() {
            return {
                systemHealth: {
                    status: 'healthy',
                    total_groups: 0,
                    healthy_groups: 0,
                    total_keys: 0,
                    active_keys: 0,
                    uptime: 0
                },
                providerStatuses: {},
                providerModels: {},
                selectedProvider: '',
                loadingModels: false,
                lastUpdate: new Date(),

                // 密钥状态相关
                keyStatus: {
                    total_keys: 0,
                    total_valid: 0,
                    total_invalid: 0,
                    last_updated: null,
                    groups: {}
                },

                // 代理密钥管理相关
                showProxyKeyModal: false,
                showGenerateProxyKeyForm: false,
                showEditProxyKeyModal: false,
                proxyKeys: [],
                newProxyKey: {
                    name: '',
                    description: '',
                    allowedGroups: []
                },
                editingProxyKey: {
                    id: '',
                    name: '',
                    description: '',
                    is_active: true,
                    allowedGroups: []
                },
                // 代理密钥分页和搜索
                proxyKeySearch: '',
                proxyKeyPage: 1,
                proxyKeyPageSize: 10,
                proxyKeyPagination: {
                    page: 1,
                    page_size: 10,
                    total: 0,
                    total_pages: 0,
                    has_prev: false,
                    has_next: false
                },
                loadingKeyStatus: false,
                loadingValidation: false,
                loadingSystemHealth: false,
                loadingProviderStatuses: false,
                keyStatusTimer: null,

                // 提供商筛选和分页
                providerSearchQuery: '',
                providerStatusFilter: '',
                providerTypeFilter: '',
                filteredProviders: [],
                providerPage: 1,
                providersPerPage: 5,
                validatingAllKeys: false,
                validatingGroups: {}, // 跟踪每个分组的验证状态

                // 分组管理相关
                showCreateGroupModal: false,
                showEditGroupModal: false,
                showBatchAddModal: false,
                submittingGroup: false,
                editingGroupId: '',
                message: '',
                messageType: 'success',

                // 密钥分页相关
                selectedKeys: [],
                keyPage: 1,
                keysPerPage: 5,
                batchKeysText: '',

                // 密钥验证相关
                validatingKeys: false,
                keyValidationStatus: {}, // 存储每个密钥的验证状态
                invalidKeyIndexes: [], // 存储无效密钥的索引

                // 模型相关
                availableModels: [],
                filteredModels: [],
                loadingModels: false,
                modelSearchQuery: '',

                groupFormData: {
                    group_id: '',
                    name: '',
                    provider_type: '',
                    base_url: '',
                    enabled: true,
                    timeout: 30,
                    max_retries: 3,
                    rotation_strategy: 'round_robin',
                    api_keys: [''],
                    models: [],
                    use_native_response: false,
                    rpm_limit: 0
                },
                modelsText: '',

                // JSON请求参数相关
                requestParamsText: '',
                showRequestParamsHelp: false,
                requestParamsValidationMessage: '',
                requestParamsValidationError: false,

                // 模型重命名相关
                modelMappings: [],
                showModelMappingHelp: false,

                // 计算属性
                get providerPageOffset() {
                    return (this.providerPage - 1) * this.providersPerPage;
                },

                get paginatedProviders() {
                    const start = this.providerPageOffset;
                    const end = start + this.providersPerPage;
                    const providersArray = Object.entries(this.filteredProviders);
                    return providersArray.slice(start, end).reduce((acc, [groupId, provider]) => {
                        acc[groupId] = provider;
                        return acc;
                    }, {});
                },

                get totalProviderPages() {
                    return Math.ceil(Object.keys(this.filteredProviders).length / this.providersPerPage);
                },

                // 分组管理计算属性
                get keyPageOffset() {
                    return (this.keyPage - 1) * this.keysPerPage;
                },

                get paginatedKeys() {
                    const start = this.keyPageOffset;
                    const end = start + this.keysPerPage;
                    return this.groupFormData.api_keys.slice(start, end);
                },

                get totalKeyPages() {
                    return Math.ceil(this.groupFormData.api_keys.length / this.keysPerPage);
                },

                // 系统状态计算属性
                get averageResponseTime() {
                    const providers = Object.values(this.providerStatuses);
                    if (providers.length === 0) return 0;

                    const totalTime = providers.reduce((sum, provider) => {
                        return sum + (provider.response_time || 0);
                    }, 0);

                    return totalTime / providers.length;
                },



                async init() {
                    // 初始化验证状态对象
                    this.validatingGroups = {};

                    // 系统健康状态和提供商分组状态需要默认展示
                    await this.loadSystemHealth();
                    await this.loadProviderStatuses();
                    await this.loadProxyKeys();

                    // 加载已保存的验证状态（初始化时不显示消息）
                    await this.loadPersistedValidationStatus(false);
                    this.filterProviders();

                    // 启动系统健康状态的定时刷新（只刷新第一排的系统信息）
                    this.startSystemHealthRefresh();

                    // 添加页面可见性变化监听器，确保用户回到页面时数据是最新的
                    document.addEventListener('visibilitychange', () => {
                        if (!document.hidden) {
                            // 页面变为可见时，刷新数据
                            this.loadProviderStatuses();
                        }
                    });
                },

                async loadSystemHealth() {
                    this.loadingSystemHealth = true;
                    try {
                        const response = await fetch('/admin/health/system');
                        if (response.ok) {
                            this.systemHealth = await response.json();
                            // this.showMessage('系统健康状态已更新', 'success');
                        } else {
                            throw new Error('获取系统健康状态失败');
                        }
                    } catch (error) {
                        console.error('Failed to load system health:', error);
                        this.showMessage('加载系统健康状态失败: ' + error.message, 'error');
                    } finally {
                        this.loadingSystemHealth = false;
                    }
                },

                async loadProviderStatuses() {
                    this.loadingProviderStatuses = true;
                    try {
                        const response = await fetch('/admin/groups');
                        if (response.ok) {
                            const data = await response.json();
                            this.providerStatuses = data.groups || {};
                            this.filterProviders();
                            // this.showMessage('提供商分组状态已更新', 'success');
                        } else {
                            throw new Error('获取提供商分组状态失败');
                        }
                    } catch (error) {
                        console.error('Failed to load provider statuses:', error);
                        this.showMessage('加载提供商分组状态失败: ' + error.message, 'error');
                    } finally {
                        this.loadingProviderStatuses = false;
                    }
                },

                async refreshData() {
                    // 手动刷新时刷新所有数据
                    await this.refreshSystemData();
                },

                async refreshProviderHealth() {
                    await this.loadProviderStatuses();
                    this.lastUpdate = new Date();
                },

                async checkProviderHealth(groupId) {
                    try {
                        const response = await fetch(`/admin/health/providers/${groupId}`);
                        if (response.ok) {
                            const data = await response.json();
                            this.providerStatuses[groupId] = data;
                        }
                    } catch (error) {
                        console.error('Failed to check provider health:', error);
                    }
                },

                async loadProviderModels() {
                    this.loadingModels = true;
                    try {
                        const url = this.selectedProvider
                            ? `/admin/models/${this.selectedProvider}`
                            : '/admin/models';

                        const response = await fetch(url);
                        if (response.ok) {
                            const data = await response.json();
                            this.providerModels = data.data || {};
                        }
                    } catch (error) {
                        console.error('Failed to load models:', error);
                    } finally {
                        this.loadingModels = false;
                    }
                },

                // 移除自动刷新功能
                // toggleAutoRefresh() {
                //     this.autoRefresh = !this.autoRefresh;
                //     if (this.autoRefresh) {
                //         this.startAutoRefresh();
                //     } else {
                //         this.stopAutoRefresh();
                //     }
                // },

                // 密钥状态相关方法
                async loadKeyStatus() {
                    this.loadingKeyStatus = true;
                    try {
                        const response = await fetch('/admin/keys/status');
                        if (response.ok) {
                            const data = await response.json();
                            if (data.success) {
                                // 计算总计数据
                                let totalKeys = 0;
                                let totalValid = 0;
                                let totalInvalid = 0;

                                for (const groupId in data.data) {
                                    const groupData = data.data[groupId];
                                    totalKeys += groupData.total_keys;
                                    totalValid += groupData.valid_keys;
                                    totalInvalid += groupData.invalid_keys;
                                }

                                this.keyStatus = {
                                    total_keys: totalKeys,
                                    total_valid: totalValid,
                                    total_invalid: totalInvalid,
                                    last_updated: new Date().toLocaleString('zh-CN'),
                                    groups: data.data
                                };
                            }
                        }
                    } catch (error) {
                        console.error('Failed to load key status:', error);
                    } finally {
                        this.loadingKeyStatus = false;
                    }
                },

                async refreshKeyStatus() {
                    await this.loadKeyStatus();
                    // 移除自动加载验证状态，改为手动触发
                    // await this.loadPersistedValidationStatus();
                },

                // 加载所有分组的持久化验证状态
                async loadPersistedValidationStatus(showMessage = true) {
                    this.loadingValidation = true;
                    try {
                        // 获取所有分组ID
                        const groupIds = Object.keys(this.providerStatuses || {});
                        let processedGroups = 0;

                        // 为每个分组加载验证状态
                        for (const groupId of groupIds) {
                            try {
                                const response = await fetch(`/admin/keys/validation/${groupId}`);
                                if (response.ok) {
                                    const data = await response.json();
                                    if (data.success && data.validation_status) {
                                        // 统计验证状态
                                        const validationStatus = data.validation_status;
                                        let validCount = 0;
                                        let invalidCount = 0;
                                        let totalCount = 0;

                                        for (const [apiKey, status] of Object.entries(validationStatus)) {
                                            totalCount++;
                                            if (status.is_valid === true) {
                                                validCount++;
                                            } else if (status.is_valid === false) {
                                                invalidCount++;
                                            }
                                        }

                                        // 更新密钥状态数据
                                        if (!this.keyStatus.groups) {
                                            this.keyStatus.groups = {};
                                        }
                                        this.keyStatus.groups[groupId] = {
                                            valid_keys: validCount,
                                            invalid_keys: invalidCount,
                                            total_keys: totalCount,
                                            last_validated: new Date().toISOString()
                                        };
                                    }
                                }
                                processedGroups++;
                            } catch (error) {
                                console.error(`Failed to load validation status for group ${groupId}:`, error);
                                processedGroups++;
                            }
                        }

                        // 重新计算总计数据
                        if (this.keyStatus.groups) {
                            let totalKeys = 0;
                            let totalValid = 0;
                            let totalInvalid = 0;

                            for (const groupData of Object.values(this.keyStatus.groups)) {
                                totalKeys += groupData.total_keys || 0;
                                totalValid += groupData.valid_keys || 0;
                                totalInvalid += groupData.invalid_keys || 0;
                            }

                            this.keyStatus.total_keys = totalKeys;
                            this.keyStatus.total_valid = totalValid;
                            this.keyStatus.total_invalid = totalInvalid;
                            this.keyStatus.last_updated = new Date().toLocaleString();
                        }

                        // 显示完成消息（仅在手动调用时显示）
                        if (showMessage) {
                            this.showMessage(`已检查 ${processedGroups} 个分组的密钥验证状态`, 'success');
                        }
                    } catch (error) {
                        console.error('Failed to load persisted validation status:', error);
                        this.showMessage('加载密钥验证状态失败: ' + error.message, 'error');
                    } finally {
                        this.loadingValidation = false;
                    }
                },



                // 提供商筛选和管理方法
                filterProviders(resetPage = true) {
                    let providers = Object.entries(this.providerStatuses);

                    // 按创建时间倒序排序（后端已经排序，但为了确保一致性）
                    providers.sort(([, a], [, b]) => {
                        const aTime = a.created_at ? new Date(a.created_at) : new Date(0);
                        const bTime = b.created_at ? new Date(b.created_at) : new Date(0);
                        return bTime - aTime; // 倒序
                    });

                    // 搜索筛选
                    if (this.providerSearchQuery.trim()) {
                        const query = this.providerSearchQuery.toLowerCase();
                        providers = providers.filter(([groupId, provider]) =>
                            provider.group_name.toLowerCase().includes(query) ||
                            provider.provider_type.toLowerCase().includes(query) ||
                            groupId.toLowerCase().includes(query)
                        );
                    }

                    // 状态筛选
                    if (this.providerStatusFilter) {
                        providers = providers.filter(([groupId, provider]) => {
                            if (this.providerStatusFilter === 'healthy') {
                                return provider.healthy;
                            } else if (this.providerStatusFilter === 'unhealthy') {
                                return !provider.healthy;
                            }
                            return true;
                        });
                    }

                    // 类型筛选
                    if (this.providerTypeFilter) {
                        providers = providers.filter(([groupId, provider]) =>
                            provider.provider_type === this.providerTypeFilter
                        );
                    }

                    // 转换为对象格式，保持排序顺序
                    this.filteredProviders = {};
                    providers.forEach(([groupId, provider]) => {
                        this.filteredProviders[groupId] = provider;
                    });

                    // 重置页码（可选）
                    if (resetPage) {
                        this.providerPage = 1;
                    }
                },

                async validateAllKeys() {
                    this.validatingAllKeys = true;
                    try {
                        await this.loadKeyStatus();
                        // 检查是否有失效密钥
                        const hasInvalidKeys = this.keyStatus.total_invalid > 0;
                        if (hasInvalidKeys) {
                            // 如果有失效密钥，可以选择打开分组管理页面
                            if (confirm(`发现 ${this.keyStatus.total_invalid} 个失效密钥，是否打开分组管理页面进行处理？`)) {
                                window.open('/groups', '_blank');
                            }
                        } else {
                            alert('所有密钥都是有效的！');
                        }
                    } catch (error) {
                        console.error('Failed to validate keys:', error);
                        alert('验证密钥时出错，请稍后重试');
                    } finally {
                        this.validatingAllKeys = false;
                    }
                },

                // 验证单个分组的密钥
                async validateGroupKeys(groupId, provider) {
                    // 确保 validatingGroups 对象存在并设置验证状态
                    if (!this.validatingGroups) {
                        this.validatingGroups = {};
                    }
                    this.validatingGroups[groupId] = true;

                    // 强制更新UI
                    this.$nextTick(() => {
                        this.validatingGroups = { ...this.validatingGroups };
                    });

                    try {
                        // 获取分组的完整数据
                        const response = await fetch('/admin/groups/manage');
                        if (!response.ok) {
                            throw new Error('Failed to fetch group data');
                        }

                        const data = await response.json();
                        const groupData = data.groups[groupId];

                        if (!groupData || !groupData.api_keys || groupData.api_keys.length === 0) {
                            alert('该分组没有配置API密钥');
                            return;
                        }

                        // 过滤掉空密钥
                        const validKeys = groupData.api_keys.filter(key => key && key.trim().length > 0);

                        if (validKeys.length === 0) {
                            alert('该分组没有有效的API密钥');
                            return;
                        }

                        // 发送验证请求
                        const validateResponse = await fetch(`/admin/keys/validate/${groupId}`, {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                            },
                            body: JSON.stringify({
                                api_keys: validKeys
                            })
                        });

                        if (!validateResponse.ok) {
                            throw new Error('验证请求失败');
                        }

                        const result = await validateResponse.json();

                        if (result.success) {
                            const validCount = result.valid_keys || 0;
                            const invalidCount = result.invalid_keys || 0;
                            const totalCount = result.total_keys || 0;

                            // 更新本地密钥状态数据
                            if (!this.keyStatus.groups) {
                                this.keyStatus.groups = {};
                            }
                            this.keyStatus.groups[groupId] = {
                                valid_keys: validCount,
                                invalid_keys: invalidCount,
                                total_keys: totalCount,
                                last_validated: new Date().toISOString()
                            };

                            // 显示结果
                            const message = `分组 "${provider.group_name}" 密钥验证完成：\n\n` +
                                          `✅ 有效密钥：${validCount}\n` +
                                          `❌ 无效密钥：${invalidCount}\n` +
                                          `📊 总计：${totalCount}\n\n` +
                                          `验证结果已保存到密钥管理列表中`;

                            alert(message);

                            // 刷新提供商状态和密钥状态
                            await this.loadProviderStatuses();
                            await this.refreshKeyStatus();
                        } else {
                            throw new Error(result.message || '验证失败');
                        }

                    } catch (error) {
                        console.error('验证分组密钥失败:', error);
                        alert(`验证分组密钥失败：${error.message}`);
                    } finally {
                        // 清除验证状态
                        if (this.validatingGroups) {
                            this.validatingGroups[groupId] = false;
                            // 强制更新UI
                            this.$nextTick(() => {
                                this.validatingGroups = { ...this.validatingGroups };
                            });
                        }
                    }
                },

                showProviderDetails(groupId, provider) {
                    const keyStatusData = this.keyStatus.groups[groupId];
                    let detailsHtml = `
                        <div class="space-y-4">
                            <div>
                                <h3 class="text-lg font-semibold">${provider.group_name} (${groupId})</h3>
                                <p class="text-sm text-gray-600">${provider.provider_type.toUpperCase()}</p>
                            </div>

                            <div class="grid grid-cols-2 gap-4 text-sm">
                                <div>
                                    <span class="font-medium">状态:</span>
                                    <span class="${provider.healthy ? 'text-green-600' : 'text-red-600'}">${provider.healthy ? '健康' : '异常'}</span>
                                </div>
                                <div>
                                    <span class="font-medium">响应时间:</span>
                                    <span>${this.formatResponseTime(provider.response_time)}</span>
                                </div>
                                <div>
                                    <span class="font-medium">总密钥数:</span>
                                    <span>${provider.total_keys}</span>
                                </div>
                                <div>
                                    <span class="font-medium">活跃密钥:</span>
                                    <span>${provider.active_keys}</span>
                                </div>
                            </div>
                    `;

                    if (keyStatusData) {
                        detailsHtml += `
                            <div class="border-t pt-4">
                                <h4 class="font-medium mb-2">密钥验证状态:</h4>
                                <div class="grid grid-cols-3 gap-4 text-sm">
                                    <div class="text-center">
                                        <div class="text-lg font-semibold text-green-600">${keyStatusData.valid_keys}</div>
                                        <div class="text-gray-600">有效</div>
                                    </div>
                                    <div class="text-center">
                                        <div class="text-lg font-semibold text-red-600">${keyStatusData.invalid_keys}</div>
                                        <div class="text-gray-600">无效</div>
                                    </div>
                                    <div class="text-center">
                                        <div class="text-lg font-semibold text-gray-600">${keyStatusData.total_keys}</div>
                                        <div class="text-gray-600">总计</div>
                                    </div>
                                </div>
                                <p class="text-xs text-gray-500 mt-2">测试模型: ${keyStatusData.test_model}</p>
                                <p class="text-xs text-gray-500">最后检查: ${keyStatusData.last_checked}</p>
                            </div>
                        `;
                    }

                    if (provider.last_error) {
                        detailsHtml += `
                            <div class="border-t pt-4">
                                <h4 class="font-medium mb-2 text-red-600">错误信息:</h4>
                                <p class="text-sm text-red-700 bg-red-50 p-2 rounded">${provider.last_error}</p>
                            </div>
                        `;
                    }

                    detailsHtml += '</div>';

                    // 这里可以使用一个模态框来显示详情，暂时使用alert
                    const tempDiv = document.createElement('div');
                    tempDiv.innerHTML = detailsHtml;
                    alert(`${provider.group_name} 详细信息\n\n状态: ${provider.healthy ? '健康' : '异常'}\n响应时间: ${this.formatResponseTime(provider.response_time)}\n密钥: ${provider.active_keys}/${provider.total_keys}\n最后检查: ${this.formatDate(provider.last_check)}`);
                },

                // 分组管理方法
                openCreateGroupModal() {
                    this.resetGroupForm();
                    this.showCreateGroupModal = true;
                },

                // 强制刷新表单UI显示
                forceRefreshFormUI() {
                    setTimeout(() => {
                        // 获取所有需要更新的表单元素
                        const formElements = [
                            { selector: 'input[x-model="groupFormData.rpm_limit"]', value: this.groupFormData.rpm_limit, event: 'input' },
                            { selector: 'input[x-model="groupFormData.use_native_response"]', value: this.groupFormData.use_native_response, event: 'change', isCheckbox: true },
                            { selector: 'input[x-model="groupFormData.timeout"]', value: this.groupFormData.timeout, event: 'input' },
                            { selector: 'input[x-model="groupFormData.max_retries"]', value: this.groupFormData.max_retries, event: 'input' }
                        ];

                        formElements.forEach(element => {
                            const el = document.querySelector(element.selector);
                            if (el) {
                                if (element.isCheckbox) {
                                    el.checked = Boolean(element.value);
                                } else {
                                    el.value = element.value;
                                }
                                el.dispatchEvent(new Event(element.event, { bubbles: true }));
                            }
                        });
                    }, 100);
                },

                async editGroup(groupId, provider) {
                    this.editingGroupId = groupId;

                    try {
                        // 从分组管理接口获取完整的分组数据
                        const response = await fetch('/admin/groups/manage');
                        if (!response.ok) {
                            throw new Error('Failed to fetch group data');
                        }

                        const data = await response.json();
                        const fullGroupData = data.groups[groupId];

                        if (!fullGroupData) {
                            throw new Error('Group data not found');
                        }

                        // 确保API密钥数组至少有一个空元素
                        let apiKeys = [''];
                        if (fullGroupData.api_keys && Array.isArray(fullGroupData.api_keys) && fullGroupData.api_keys.length > 0) {
                            apiKeys = [...fullGroupData.api_keys];
                        }

                        this.groupFormData = {
                            group_id: groupId,
                            name: fullGroupData.group_name || '',
                            provider_type: fullGroupData.provider_type || '',
                            base_url: fullGroupData.base_url || '',
                            enabled: fullGroupData.enabled !== false,
                            timeout: parseInt(fullGroupData.timeout) || 30,
                            max_retries: parseInt(fullGroupData.max_retries) || 3,
                            rotation_strategy: fullGroupData.rotation_strategy || 'round_robin',
                            api_keys: apiKeys,
                            models: fullGroupData.models || [],
                            use_native_response: Boolean(fullGroupData.use_native_response),
                            rpm_limit: parseInt(fullGroupData.rpm_limit) || 0
                        };

                        this.modelsText = (fullGroupData.models || []).join('\n');

                        // 加载JSON请求参数
                        if (fullGroupData.request_params && Object.keys(fullGroupData.request_params).length > 0) {
                            this.requestParamsText = JSON.stringify(fullGroupData.request_params, null, 2);
                        } else {
                            this.requestParamsText = '';
                        }

                        // 加载模型映射
                        this.modelMappings = [];
                        if (fullGroupData.model_mappings && Object.keys(fullGroupData.model_mappings).length > 0) {
                            for (const [alias, original] of Object.entries(fullGroupData.model_mappings)) {
                                this.modelMappings.push({ alias: alias, original: original });
                            }
                        }

                        // 使用setTimeout确保所有数据都已经设置完成
                        setTimeout(() => {
                            // 强制触发响应式更新
                            this.modelMappings = [...this.modelMappings];
                        }, 100);

                        this.selectedKeys = [];
                        this.keyPage = 1;
                        this.availableModels = [];
                        this.filteredModels = [];
                        this.modelSearchQuery = '';
                        this.keyValidationStatus = {};
                        this.invalidKeyIndexes = [];
                        this.requestParamsValidationMessage = '';
                        this.requestParamsValidationError = false;



                        this.showEditGroupModal = true;

                        // 延迟刷新表单UI，确保模态框完全显示后再刷新
                        setTimeout(() => {
                            this.forceRefreshFormUI();
                        }, 200);

                        // 移除自动加载验证状态，改为手动触发
                        // await this.loadKeyValidationStatus(groupId);

                    } catch (error) {
                        console.error('获取分组数据失败:', error);
                        alert('获取分组数据失败，请稍后重试');
                    }
                },

                async toggleGroup(groupId, provider) {
                    try {
                        const response = await fetch(`/admin/groups/${groupId}/toggle`, {
                            method: 'POST'
                        });

                        const data = await response.json();

                        if (response.ok) {
                            this.showMessage(data.message, 'success');
                            // 重新加载提供商状态以反映分组状态变化
                            await this.loadProviderStatuses();
                        } else {
                            this.showMessage(data.message || '操作失败', 'error');
                        }
                    } catch (error) {
                        this.showMessage('网络错误: ' + error.message, 'error');
                    }
                },

                async deleteGroup(groupId, provider) {
                    if (!confirm(`确定要删除分组 "${provider.group_name}" 吗？此操作不可恢复。`)) {
                        return;
                    }

                    try {
                        const response = await fetch(`/admin/groups/${groupId}`, {
                            method: 'DELETE'
                        });

                        const data = await response.json();

                        if (response.ok) {
                            this.showMessage(data.message, 'success');
                            // 重新加载提供商状态以移除已删除的分组
                            await this.loadProviderStatuses();
                        } else {
                            this.showMessage(data.message || '删除失败', 'error');
                        }
                    } catch (error) {
                        this.showMessage('网络错误: ' + error.message, 'error');
                    }
                },

                async submitGroupForm() {
                    this.submittingGroup = true;
                    try {
                        // 保存当前的页面状态（用于编辑模式保持位置）
                        const currentPage = this.providerPage;
                        const currentSearchQuery = this.providerSearchQuery;
                        const currentStatusFilter = this.providerStatusFilter;
                        const currentTypeFilter = this.providerTypeFilter;

                        // 处理模型列表 - 合并选择的模型和手动输入的模型
                        const manualModels = this.modelsText.split('\n')
                            .map(line => line.trim())
                            .filter(line => line.length > 0);

                        // 去重合并模型列表
                        const allModels = [...new Set([...this.groupFormData.models, ...manualModels])];
                        this.groupFormData.models = allModels;

                        // 过滤空的API密钥
                        this.groupFormData.api_keys = this.groupFormData.api_keys.filter(key => key.trim().length > 0);

                        // 处理JSON请求参数
                        if (this.requestParamsText.trim()) {
                            try {
                                this.groupFormData.request_params = JSON.parse(this.requestParamsText.trim());
                                this.requestParamsValidationMessage = '';
                                this.requestParamsValidationError = false;
                            } catch (e) {
                                this.requestParamsValidationMessage = 'JSON格式错误: ' + e.message;
                                this.requestParamsValidationError = true;
                                this.submittingGroup = false;
                                return;
                            }
                        } else {
                            this.groupFormData.request_params = {};
                        }

                        // 处理模型映射
                        const modelMappings = {};
                        for (const mapping of this.modelMappings) {
                            if (mapping.alias && mapping.alias.trim() && mapping.original && mapping.original.trim()) {
                                modelMappings[mapping.alias.trim()] = mapping.original.trim();
                            }
                        }
                        this.groupFormData.model_mappings = modelMappings;

                        // 确保数字字段是正确的类型
                        this.groupFormData.timeout = parseInt(this.groupFormData.timeout) || 30;
                        this.groupFormData.max_retries = parseInt(this.groupFormData.max_retries) || 3;
                        this.groupFormData.rpm_limit = parseInt(this.groupFormData.rpm_limit) || 0;

                        const url = this.showCreateGroupModal ? '/admin/groups' : `/admin/groups/${this.editingGroupId}`;
                        const method = this.showCreateGroupModal ? 'POST' : 'PUT';

                        const response = await fetch(url, {
                            method: method,
                            headers: {
                                'Content-Type': 'application/json'
                            },
                            body: JSON.stringify(this.groupFormData)
                        });

                        const data = await response.json();

                        if (response.ok) {
                            this.showMessage(data.message, 'success');

                            // 如果是编辑模式，更新本地数据而不重新加载整个列表
                            if (!this.showCreateGroupModal && this.editingGroupId) {
                                // 更新本地providerStatuses中的数据
                                if (this.providerStatuses[this.editingGroupId]) {
                                    // 更新用户可编辑的字段，保留健康检查相关字段
                                    Object.assign(this.providerStatuses[this.editingGroupId], {
                                        group_name: this.groupFormData.name,  // 注意：groupFormData中使用的是name字段
                                        provider_type: this.groupFormData.provider_type,
                                        base_url: this.groupFormData.base_url,
                                        models: this.groupFormData.models,
                                        model_mappings: this.groupFormData.model_mappings,
                                        use_native_response: this.groupFormData.use_native_response,
                                        rpm_limit: this.groupFormData.rpm_limit,
                                        timeout: this.groupFormData.timeout,
                                        max_retries: this.groupFormData.max_retries,
                                        request_params: this.groupFormData.request_params,
                                        enabled: this.groupFormData.enabled,
                                        rotation_strategy: this.groupFormData.rotation_strategy
                                        // 注意：不更新 healthy, last_check, response_time, last_error, total_keys, active_keys 等健康检查字段
                                    });
                                }

                                this.closeGroupModal();

                                // 恢复页面状态
                                this.providerPage = currentPage;
                                this.providerSearchQuery = currentSearchQuery;
                                this.providerStatusFilter = currentStatusFilter;
                                this.providerTypeFilter = currentTypeFilter;

                                // 重新过滤提供商以反映更新，但不重置页面位置
                                this.filterProviders(false);
                            } else {
                                // 创建模式：重新加载提供商状态以显示新添加的分组
                                this.closeGroupModal();
                                await this.loadProviderStatuses();
                            }

                            // 额外的数据验证，确保保存的数据正确
                            setTimeout(async () => {
                                if (this.editingGroupId && this.providerStatuses[this.editingGroupId]) {
                                    const savedData = this.providerStatuses[this.editingGroupId];
                                }
                            }, 500);
                        } else {
                            this.showMessage(data.message || '操作失败', 'error');
                        }
                    } catch (error) {
                        this.showMessage('网络错误: ' + error.message, 'error');
                    } finally {
                        this.submittingGroup = false;
                    }
                },

                closeGroupModal() {
                    this.showCreateGroupModal = false;
                    this.showEditGroupModal = false;
                    this.showBatchAddModal = false;
                    this.editingGroupId = '';
                    this.resetGroupForm();
                },

                resetGroupForm() {
                    this.groupFormData = {
                        group_id: '',
                        name: '',
                        provider_type: '',
                        base_url: '',
                        enabled: true,
                        timeout: 30,
                        max_retries: 3,
                        rotation_strategy: 'round_robin',
                        api_keys: [''],
                        models: [],
                        use_native_response: false,
                        rpm_limit: 0
                    };
                    this.modelsText = '';
                    this.selectedKeys = [];
                    this.keyPage = 1;
                    this.availableModels = [];
                    this.filteredModels = [];
                    this.modelSearchQuery = '';
                    this.batchKeysText = '';
                    this.keyValidationStatus = {};
                    this.invalidKeyIndexes = [];

                    // 重置JSON参数相关字段
                    this.requestParamsText = '';
                    this.showRequestParamsHelp = false;
                    this.requestParamsValidationMessage = '';
                    this.requestParamsValidationError = false;

                    // 重置模型映射相关字段
                    this.modelMappings = [];
                    this.showModelMappingHelp = false;
                },

                // 提供商类型变化时自动填充Base URL
                onProviderTypeChange() {
                    const defaultBaseUrls = {
                        'openai': 'https://api.openai.com/v1',
                        'anthropic': 'https://api.anthropic.com',
                        'gemini': 'https://generativelanguage.googleapis.com',
                        'azure_openai': 'https://your-resource-name.openai.azure.com',
                        'openrouter': 'https://openrouter.ai/api/v1'
                    };

                    const providerType = this.groupFormData.provider_type;
                    if (providerType && defaultBaseUrls[providerType]) {
                        // 只有当Base URL为空或者是默认值时才自动填充
                        const currentBaseUrl = this.groupFormData.base_url;
                        const isDefaultUrl = Object.values(defaultBaseUrls).includes(currentBaseUrl);

                        if (!currentBaseUrl || isDefaultUrl) {
                            this.groupFormData.base_url = defaultBaseUrls[providerType];
                        }
                    }
                },

                addGroupApiKey() {
                    this.groupFormData.api_keys.push('');
                },

                removeGroupApiKey(index) {
                    if (this.groupFormData.api_keys.length > 1) {
                        this.groupFormData.api_keys.splice(index, 1);
                        // 调整页码如果当前页没有数据了
                        if (this.keyPageOffset >= this.groupFormData.api_keys.length && this.keyPage > 1) {
                            this.keyPage--;
                        }
                        // 清除选中状态
                        this.selectedKeys = this.selectedKeys.filter(i => i < this.groupFormData.api_keys.length);
                    }
                },

                // 批量添加密钥
                addBatchKeys() {
                    const keys = this.batchKeysText.split('\n')
                        .map(line => line.trim())
                        .filter(line => line.length > 0);

                    if (keys.length > 0) {
                        // 移除空的密钥项
                        this.groupFormData.api_keys = this.groupFormData.api_keys.filter(key => key.trim().length > 0);
                        // 添加新密钥
                        this.groupFormData.api_keys.push(...keys);
                        // 如果原来没有密钥，确保至少有一个空项
                        if (this.groupFormData.api_keys.length === 0) {
                            this.groupFormData.api_keys.push('');
                        }
                        this.batchKeysText = '';
                        this.showBatchAddModal = false;
                        this.showMessage(`成功添加 ${keys.length} 个密钥`, 'success');
                    }
                },

                // 删除选中的密钥
                deleteSelectedKeys() {
                    if (this.selectedKeys.length === 0) return;

                    if (!confirm(`确定要删除选中的 ${this.selectedKeys.length} 个密钥吗？`)) {
                        return;
                    }

                    // 按索引倒序删除，避免索引变化问题
                    const sortedIndexes = [...this.selectedKeys].sort((a, b) => b - a);
                    sortedIndexes.forEach(index => {
                        this.groupFormData.api_keys.splice(index, 1);
                    });

                    // 确保至少有一个空项
                    if (this.groupFormData.api_keys.length === 0) {
                        this.groupFormData.api_keys.push('');
                    }

                    // 清除选中状态
                    this.selectedKeys = [];

                    // 调整页码
                    if (this.keyPageOffset >= this.groupFormData.api_keys.length && this.keyPage > 1) {
                        this.keyPage--;
                    }

                    this.showMessage(`成功删除 ${sortedIndexes.length} 个密钥`, 'success');
                },

                // 删除失效密钥
                deleteInvalidKeys() {
                    // 找出所有失效的密钥索引
                    const invalidIndexes = [];
                    this.groupFormData.api_keys.forEach((key, index) => {
                        if (key.trim() && this.keyValidationStatus[index] === 'invalid') {
                            invalidIndexes.push(index);
                        }
                    });

                    if (invalidIndexes.length === 0) {
                        this.showMessage('没有找到失效的密钥', 'info');
                        return;
                    }

                    if (!confirm(`确定要删除 ${invalidIndexes.length} 个失效密钥吗？`)) {
                        return;
                    }

                    // 按索引倒序删除
                    const sortedIndexes = [...invalidIndexes].sort((a, b) => b - a);
                    sortedIndexes.forEach(index => {
                        this.groupFormData.api_keys.splice(index, 1);
                    });

                    // 确保至少有一个空项
                    if (this.groupFormData.api_keys.length === 0) {
                        this.groupFormData.api_keys.push('');
                    }

                    // 重新构建验证状态，移除已删除密钥的状态
                    const newValidationStatus = {};
                    const newInvalidIndexes = [];
                    this.groupFormData.api_keys.forEach((key, newIndex) => {
                        // 找到原来的索引对应的状态
                        for (let oldIndex = 0; oldIndex < this.groupFormData.api_keys.length + sortedIndexes.length; oldIndex++) {
                            if (!sortedIndexes.includes(oldIndex) && this.keyValidationStatus[oldIndex]) {
                                const adjustedIndex = oldIndex - sortedIndexes.filter(idx => idx < oldIndex).length;
                                if (adjustedIndex === newIndex) {
                                    newValidationStatus[newIndex] = this.keyValidationStatus[oldIndex];
                                    if (this.keyValidationStatus[oldIndex] === 'invalid') {
                                        newInvalidIndexes.push(newIndex);
                                    }
                                    break;
                                }
                            }
                        }
                    });

                    this.keyValidationStatus = newValidationStatus;
                    this.invalidKeyIndexes = newInvalidIndexes;

                    // 调整页码
                    if (this.keyPageOffset >= this.groupFormData.api_keys.length && this.keyPage > 1) {
                        this.keyPage--;
                    }

                    this.showMessage(`成功删除 ${sortedIndexes.length} 个失效密钥`, 'success');
                },

                // 验证密钥
                async validateKeys() {
                    const validKeys = this.groupFormData.api_keys.filter(key => key.trim().length > 0);
                    if (validKeys.length === 0) {
                        this.showMessage('请先添加至少一个API密钥', 'error');
                        return;
                    }

                    if (!this.groupFormData.provider_type || !this.groupFormData.base_url) {
                        this.showMessage('请先填写提供商类型和Base URL', 'error');
                        return;
                    }

                    this.validatingKeys = true;
                    this.keyValidationStatus = {};
                    this.invalidKeyIndexes = [];

                    try {
                        // 标记所有密钥为验证中
                        this.groupFormData.api_keys.forEach((key, index) => {
                            if (key.trim().length > 0) {
                                this.keyValidationStatus[index] = 'validating';
                            }
                        });

                        // 创建临时分组配置来验证密钥
                        const tempGroupData = {
                            name: this.groupFormData.name || 'temp',
                            provider_type: this.groupFormData.provider_type,
                            base_url: this.groupFormData.base_url,
                            enabled: true,
                            timeout: this.groupFormData.timeout || 30,
                            max_retries: this.groupFormData.max_retries || 3,
                            rotation_strategy: this.groupFormData.rotation_strategy || 'round_robin',
                            api_keys: validKeys
                        };

                        const response = await fetch('/admin/keys/validate', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json'
                            },
                            body: JSON.stringify(tempGroupData)
                        });

                        if (response.ok) {
                            const data = await response.json();
                            if (data.success && data.results) {
                                let validCount = 0;
                                let invalidCount = 0;

                                // 更新验证状态
                                data.results.forEach((result, resultIndex) => {
                                    // 找到对应的原始索引
                                    let originalIndex = 0;
                                    let validKeyIndex = 0;
                                    for (let i = 0; i < this.groupFormData.api_keys.length; i++) {
                                        if (this.groupFormData.api_keys[i].trim().length > 0) {
                                            if (validKeyIndex === resultIndex) {
                                                originalIndex = i;
                                                break;
                                            }
                                            validKeyIndex++;
                                        }
                                    }

                                    if (result.valid) {
                                        this.keyValidationStatus[originalIndex] = 'valid';
                                        validCount++;
                                    } else {
                                        this.keyValidationStatus[originalIndex] = 'invalid';
                                        this.invalidKeyIndexes.push(originalIndex);
                                        invalidCount++;
                                    }
                                });

                                this.showMessage(`验证完成：${validCount} 个有效，${invalidCount} 个无效`, validCount > 0 ? 'success' : 'error');
                            } else {
                                this.showMessage('验证失败: ' + (data.message || '未知错误'), 'error');
                            }
                        } else {
                            const errorData = await response.json();
                            this.showMessage('验证失败: ' + (errorData.message || '网络错误'), 'error');
                        }
                    } catch (error) {
                        this.showMessage('验证失败: ' + error.message, 'error');
                    } finally {
                        this.validatingKeys = false;
                    }
                },

            // 获取API密钥验证状态
            async loadKeyValidationStatus(groupId) {
                if (!groupId) return;

                try {
                    const response = await fetch(`/admin/keys/validation/${groupId}`);
                    if (response.ok) {
                        const data = await response.json();
                        if (data.success && data.validation_status) {
                            // 更新密钥验证状态
                            this.keyValidationStatus = {};
                            this.invalidKeyIndexes = [];
                            this.groupFormData.api_keys.forEach((key, index) => {
                                if (key.trim() && data.validation_status[key.trim()]) {
                                    const status = data.validation_status[key.trim()];
                                    if (status.is_valid === true) {
                                        this.keyValidationStatus[index] = 'valid';
                                    } else if (status.is_valid === false) {
                                        this.keyValidationStatus[index] = 'invalid';
                                        this.invalidKeyIndexes.push(index);
                                    }
                                }
                            });
                        }
                    }
                } catch (error) {
                    console.error('Failed to load key validation status:', error);
                }
            },

                // 获取密钥验证样式类
                getKeyValidationClass(index) {
                    const status = this.keyValidationStatus[index];
                    if (status === 'valid') {
                        return 'bg-green-50 border-green-200';
                    } else if (status === 'invalid') {
                        return 'bg-red-50 border-red-200';
                    } else if (status === 'validating') {
                        return 'bg-yellow-50 border-yellow-200';
                    }
                    return '';
                },

                // 获取失效密钥数量
                getInvalidKeyCount() {
                    let count = 0;
                    this.groupFormData.api_keys.forEach((key, index) => {
                        if (key.trim() && this.keyValidationStatus[index] === 'invalid') {
                            count++;
                        }
                    });
                    return count;
                },

                // 加载可用模型
                async loadAvailableModels() {
                    // 如果是编辑模式，可以直接从现有分组加载模型
                    if (this.showEditGroupModal && this.editingGroupId) {
                        this.loadingModels = true;
                        try {
                            const response = await fetch(`/admin/models/available/${this.editingGroupId}`);

                            if (response.ok) {
                                const data = await response.json();
                                // 解析响应数据
                                const groupData = data.data[this.editingGroupId];
                                if (groupData && groupData.models && groupData.models.data) {
                                    this.availableModels = groupData.models.data;
                                    this.filterModels();
                                    this.showMessage(`成功加载 ${this.availableModels.length} 个模型`, 'success');
                                } else {
                                    this.availableModels = [];
                                    this.filterModels();
                                    this.showMessage('未找到可用模型', 'error');
                                }
                            } else {
                                const errorData = await response.json();
                                this.showMessage('加载模型失败: ' + (errorData.error?.message || '未知错误'), 'error');
                            }
                        } catch (error) {
                            this.showMessage('网络错误: ' + error.message, 'error');
                        } finally {
                            this.loadingModels = false;
                        }
                        return;
                    }

                    // 创建模式需要先验证配置
                    if (!this.groupFormData.provider_type || !this.groupFormData.base_url) {
                        this.showMessage('请先填写提供商类型和Base URL', 'error');
                        return;
                    }

                    const validKeys = this.groupFormData.api_keys.filter(key => key.trim().length > 0);
                    if (validKeys.length === 0) {
                        this.showMessage('请先添加至少一个API密钥', 'error');
                        return;
                    }

                    this.loadingModels = true;
                    try {
                        // 创建临时分组配置来测试模型加载
                        const tempGroupData = {
                            name: this.groupFormData.name || 'temp',
                            provider_type: this.groupFormData.provider_type,
                            base_url: this.groupFormData.base_url,
                            enabled: true,
                            timeout: this.groupFormData.timeout || 30,
                            max_retries: this.groupFormData.max_retries || 3,
                            rotation_strategy: this.groupFormData.rotation_strategy || 'round_robin',
                            api_keys: validKeys
                        };

                        // 使用新的按类型加载模型API
                        const response = await fetch('/admin/models/available/by-type', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json'
                            },
                            body: JSON.stringify({
                                provider_type: tempGroupData.provider_type,
                                base_url: tempGroupData.base_url,
                                api_keys: tempGroupData.api_keys,
                                timeout_seconds: tempGroupData.timeout,
                                max_retries: tempGroupData.max_retries,
                                headers: this.groupFormData.headers || {}
                            })
                        });

                        if (response.ok) {
                            const data = await response.json();
                            // 解析新API的响应格式
                            const groupData = data.data['temp-group'];
                            if (groupData && groupData.models && groupData.models.data) {
                                this.availableModels = groupData.models.data;
                                this.filterModels();
                                this.showMessage(`成功加载 ${this.availableModels.length} 个模型`, 'success');
                            } else {
                                this.availableModels = [];
                                this.filterModels();
                                this.showMessage('未找到可用模型', 'error');
                            }
                        } else {
                            const errorData = await response.json();
                            this.showMessage('加载模型失败: ' + (errorData.error || '未知错误'), 'error');
                        }
                    } catch (error) {
                        this.showMessage('网络错误: ' + error.message, 'error');
                    } finally {
                        this.loadingModels = false;
                    }
                },

                // 筛选模型
                filterModels() {
                    if (!this.modelSearchQuery.trim()) {
                        this.filteredModels = this.availableModels;
                    } else {
                        const query = this.modelSearchQuery.toLowerCase();
                        this.filteredModels = this.availableModels.filter(model =>
                            model.id.toLowerCase().includes(query) ||
                            (model.owned_by && model.owned_by.toLowerCase().includes(query))
                        );
                    }
                },

                // 清除模型搜索
                clearModelSearch() {
                    this.modelSearchQuery = '';
                    this.filterModels();
                },

                // 全选模型
                selectAllModels() {
                    this.groupFormData.models = this.filteredModels.map(model => model.id);
                },

                // 清空模型选择
                clearAllModels() {
                    this.groupFormData.models = [];
                },

                // 移除单个模型
                removeModel(modelId) {
                    this.groupFormData.models = this.groupFormData.models.filter(id => id !== modelId);
                },

                // 验证JSON请求参数格式
                validateRequestParams() {
                    if (!this.requestParamsText.trim()) {
                        this.requestParamsValidationMessage = '';
                        this.requestParamsValidationError = false;
                        return;
                    }

                    try {
                        const parsed = JSON.parse(this.requestParamsText.trim());

                        // 检查是否是对象
                        if (typeof parsed !== 'object' || Array.isArray(parsed) || parsed === null) {
                            this.requestParamsValidationMessage = 'JSON必须是一个对象';
                            this.requestParamsValidationError = true;
                            return;
                        }

                        // 检查支持的参数
                        const supportedParams = ['temperature', 'max_tokens', 'top_p', 'stop'];
                        const unsupportedParams = Object.keys(parsed).filter(key => !supportedParams.includes(key));

                        if (unsupportedParams.length > 0) {
                            this.requestParamsValidationMessage = `不支持的参数: ${unsupportedParams.join(', ')}。支持的参数: ${supportedParams.join(', ')}`;
                            this.requestParamsValidationError = true;
                            return;
                        }

                        // 验证参数值类型
                        if ('temperature' in parsed && (typeof parsed.temperature !== 'number' || parsed.temperature < 0 || parsed.temperature > 2)) {
                            this.requestParamsValidationMessage = 'temperature必须是0-2之间的数字';
                            this.requestParamsValidationError = true;
                            return;
                        }

                        if ('max_tokens' in parsed && (!Number.isInteger(parsed.max_tokens) || parsed.max_tokens <= 0)) {
                            this.requestParamsValidationMessage = 'max_tokens必须是正整数';
                            this.requestParamsValidationError = true;
                            return;
                        }

                        if ('top_p' in parsed && (typeof parsed.top_p !== 'number' || parsed.top_p < 0 || parsed.top_p > 1)) {
                            this.requestParamsValidationMessage = 'top_p必须是0-1之间的数字';
                            this.requestParamsValidationError = true;
                            return;
                        }

                        if ('stop' in parsed && !Array.isArray(parsed.stop)) {
                            this.requestParamsValidationMessage = 'stop必须是字符串数组';
                            this.requestParamsValidationError = true;
                            return;
                        }

                        this.requestParamsValidationMessage = 'JSON格式正确';
                        this.requestParamsValidationError = false;
                    } catch (e) {
                        this.requestParamsValidationMessage = 'JSON格式错误: ' + e.message;
                        this.requestParamsValidationError = true;
                    }
                },

                // 添加模型映射
                addModelMapping() {
                    this.modelMappings.push({ alias: '', original: '' });
                },

                // 移除模型映射
                removeModelMapping(index) {
                    this.modelMappings.splice(index, 1);
                },

                showMessage(msg, type = 'success') {
                    this.message = msg;
                    this.messageType = type;
                    setTimeout(() => {
                        this.message = '';
                    }, 3000);
                },

                getKeyStatusText(groupId, provider) {
                    const keyStatusData = this.keyStatus.groups[groupId];
                    if (keyStatusData) {
                        return `${keyStatusData.valid_keys}/${keyStatusData.total_keys}`;
                    }
                    return `${provider.active_keys}/${provider.total_keys}`;
                },

                // 获取详细的密钥状态数据
                getKeyStatusData(groupId, provider) {
                    const keyStatusData = this.keyStatus.groups && this.keyStatus.groups[groupId];

                    if (keyStatusData) {
                        // 如果有检测数据，使用检测结果
                        const valid = keyStatusData.valid_keys || 0;
                        const invalid = keyStatusData.invalid_keys || 0;
                        const total = keyStatusData.total_keys || 0;

                        return {
                            total: total,
                            valid: valid,
                            invalid: invalid,
                            unknown: 0 // 已检测完毕，没有未知状态
                        };
                    } else {
                        // 如果没有检测数据，使用提供商基本信息
                        const total = provider.total_keys || 0;
                        const active = provider.active_keys || 0;

                        return {
                            total: total,
                            valid: 0,
                            invalid: 0,
                            unknown: total // 所有密钥都是未检测状态
                        };
                    }
                },

                // 移除自动刷新相关方法
                // startAutoRefresh() {
                //     this.stopAutoRefresh();
                //     this.refreshTimer = setInterval(() => {
                //         this.refreshData();
                //     }, this.refreshInterval);
                // },

                // stopAutoRefresh() {
                //     if (this.refreshTimer) {
                //         clearInterval(this.refreshTimer);
                //         this.refreshTimer = null;
                //     }
                // },

                // updateRefreshInterval() {
                //     if (this.autoRefresh) {
                //         this.startAutoRefresh();
                //     }
                // },

                getStatusText(status) {
                    const statusMap = {
                        'healthy': '健康',
                        'degraded': '降级',
                        'unhealthy': '异常'
                    };
                    return statusMap[status] || '未知';
                },

                getProviderTypeClass(type) {
                    const typeClasses = {
                        'openai': 'bg-blue-100 text-blue-800',
                        'gemini': 'bg-green-100 text-green-800',
                        'anthropic': 'bg-purple-100 text-purple-800',
                        'azure_openai': 'bg-cyan-100 text-cyan-800'
                    };
                    return typeClasses[type] || 'bg-gray-100 text-gray-800';
                },

                formatDuration(nanoseconds) {
                    if (!nanoseconds) return '--';
                    const seconds = Math.floor(nanoseconds / **********);
                    const hours = Math.floor(seconds / 3600);
                    const minutes = Math.floor((seconds % 3600) / 60);
                    return `${hours}h ${minutes}m`;
                },

                formatResponseTime(nanoseconds) {
                    if (!nanoseconds) return '--';
                    const ms = Math.floor(nanoseconds / 1000000);
                    return `${ms}ms`;
                },

                formatDate(dateStr) {
                    if (!dateStr) return '--';
                    return new Date(dateStr).toLocaleString('zh-CN');
                },

                formatPercentage(value) {
                    if (value === null || value === undefined) return '0%';
                    return parseFloat(value).toFixed(2) + '%';
                },

                // 启动系统健康状态的定时刷新（只刷新第一排系统信息）
                startSystemHealthRefresh() {
                    // 立即执行一次刷新
                    this.refreshSystemHealthOnly();

                    // 设置定时器，每60秒刷新一次系统健康状态
                    setInterval(() => {
                        this.refreshSystemHealthOnly();
                    }, 60000); // 60秒刷新一次
                },

                // 只刷新系统健康状态（第一排的系统信息）
                async refreshSystemHealthOnly() {
                    try {
                        await this.loadSystemHealth();
                        this.lastUpdate = new Date();
                    } catch (error) {
                        console.error('Failed to refresh system health:', error);
                    }
                },

                // 刷新所有数据（手动刷新时使用）
                async refreshSystemData() {
                    try {
                        await Promise.all([
                            this.loadSystemHealth(),
                            this.loadProviderStatuses()
                        ]);
                        this.lastUpdate = new Date();
                    } catch (error) {
                        console.error('Failed to refresh system data:', error);
                    }
                },

                // 代理密钥管理方法
                async loadProxyKeys(page = null, search = null) {
                    try {
                        // 使用传入的参数或当前状态
                        const currentPage = page !== null ? page : this.proxyKeyPage;
                        const currentSearch = search !== null ? search : this.proxyKeySearch;

                        // 构建查询参数
                        const params = new URLSearchParams({
                            page: currentPage.toString(),
                            page_size: this.proxyKeyPageSize.toString()
                        });

                        if (currentSearch.trim()) {
                            params.append('search', currentSearch.trim());
                        }

                        const response = await fetch(`/admin/proxy-keys?${params}`);
                        const result = await response.json();

                        if (result.success) {
                            this.proxyKeys = result.keys || [];
                            this.proxyKeyPagination = result.pagination || {};
                            this.proxyKeyPage = this.proxyKeyPagination.page || 1;
                        } else {
                            console.error('加载代理密钥失败:', result);
                        }
                    } catch (error) {
                        console.error('加载代理密钥失败:', error);
                    }
                },

                async generateProxyKey() {
                    if (!this.newProxyKey.name.trim()) {
                        this.showMessage('请输入密钥名称', 'error');
                        return;
                    }

                    try {
                        const response = await fetch('/admin/proxy-keys', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                            },
                            body: JSON.stringify(this.newProxyKey)
                        });

                        const result = await response.json();

                        if (result.success) {
                            this.showMessage('代理密钥生成成功！', 'success');
                            this.showGenerateProxyKeyForm = false;
                            this.resetProxyKeyForm();
                            await this.loadProxyKeys();
                        } else {
                            this.showMessage('生成失败: ' + (result.error || '未知错误'), 'error');
                        }
                    } catch (error) {
                        console.error('生成代理密钥失败:', error);
                        this.showMessage('网络错误，请检查连接', 'error');
                    }
                },

                async deleteProxyKey(keyId) {
                    if (!confirm('确定要删除这个代理密钥吗？删除后无法恢复。')) {
                        return;
                    }

                    try {
                        const response = await fetch(`/admin/proxy-keys/${keyId}`, {
                            method: 'DELETE'
                        });

                        const result = await response.json();
                        if (result.success) {
                            this.showMessage('代理密钥删除成功！', 'success');
                            await this.loadProxyKeys();
                        } else {
                            this.showMessage('删除失败: ' + (result.error || '未知错误'), 'error');
                        }
                    } catch (error) {
                        console.error('删除代理密钥失败:', error);
                        this.showMessage('网络错误，请检查连接', 'error');
                    }
                },

                resetProxyKeyForm() {
                    this.newProxyKey = {
                        name: '',
                        description: '',
                        allowedGroups: []
                    };
                },

                toggleProxyKeyGroup(groupId, checked) {
                    if (checked) {
                        if (!this.newProxyKey.allowedGroups.includes(groupId)) {
                            this.newProxyKey.allowedGroups.push(groupId);
                        }
                    } else {
                        const index = this.newProxyKey.allowedGroups.indexOf(groupId);
                        if (index > -1) {
                            this.newProxyKey.allowedGroups.splice(index, 1);
                        }
                    }
                },

                getGroupName(groupId) {
                    const provider = this.providerStatuses[groupId];
                    return provider ? provider.group_name : groupId;
                },

                // 编辑代理密钥相关方法
                editProxyKey(key) {
                    this.editingProxyKey = {
                        id: key.id,
                        name: key.name,
                        description: key.description || '',
                        is_active: key.is_active !== false, // 默认为true
                        allowedGroups: key.allowed_groups ? [...key.allowed_groups] : []
                    };
                    this.showEditProxyKeyModal = true;
                },

                closeEditProxyKeyModal() {
                    this.showEditProxyKeyModal = false;
                    this.editingProxyKey = {
                        id: '',
                        name: '',
                        description: '',
                        is_active: true,
                        allowedGroups: []
                    };
                },

                toggleEditingProxyKeyGroup(groupId, checked) {
                    if (checked) {
                        if (!this.editingProxyKey.allowedGroups.includes(groupId)) {
                            this.editingProxyKey.allowedGroups.push(groupId);
                        }
                    } else {
                        const index = this.editingProxyKey.allowedGroups.indexOf(groupId);
                        if (index > -1) {
                            this.editingProxyKey.allowedGroups.splice(index, 1);
                        }
                    }
                },

                async updateProxyKey() {
                    if (!this.editingProxyKey.name.trim()) {
                        this.showMessage('请输入密钥名称', 'error');
                        return;
                    }

                    try {
                        const response = await fetch(`/admin/proxy-keys/${this.editingProxyKey.id}`, {
                            method: 'PUT',
                            headers: {
                                'Content-Type': 'application/json',
                            },
                            body: JSON.stringify({
                                name: this.editingProxyKey.name,
                                description: this.editingProxyKey.description,
                                is_active: this.editingProxyKey.is_active,
                                allowedGroups: this.editingProxyKey.allowedGroups
                            })
                        });

                        const result = await response.json();

                        if (result.success) {
                            this.showMessage('代理密钥更新成功！', 'success');
                            this.closeEditProxyKeyModal();
                            await this.loadProxyKeys();
                        } else {
                            this.showMessage('更新失败: ' + (result.error || '未知错误'), 'error');
                        }
                    } catch (error) {
                        console.error('更新代理密钥失败:', error);
                        this.showMessage('网络错误，请检查连接', 'error');
                    }
                },

                copyToClipboard(text) {
                    navigator.clipboard.writeText(text).then(() => {
                        this.showMessage('已复制到剪贴板', 'success');
                    }).catch(() => {
                        // 降级方案
                        const textArea = document.createElement('textarea');
                        textArea.value = text;
                        document.body.appendChild(textArea);
                        textArea.select();
                        document.execCommand('copy');
                        document.body.removeChild(textArea);
                        this.showMessage('已复制到剪贴板', 'success');
                    });
                },

                // 代理密钥搜索和分页方法
                async searchProxyKeys() {
                    this.proxyKeyPage = 1; // 搜索时重置到第一页
                    await this.loadProxyKeys(1, this.proxyKeySearch);
                },

                async goToProxyKeyPage(page) {
                    if (page >= 1 && page <= this.proxyKeyPagination.total_pages) {
                        await this.loadProxyKeys(page);
                    }
                },

                async changeProxyKeyPageSize() {
                    this.proxyKeyPage = 1; // 改变页面大小时重置到第一页
                    await this.loadProxyKeys(1);
                },

                clearProxyKeySearch() {
                    this.proxyKeySearch = '';
                    this.searchProxyKeys();
                },

                viewProviderDetails(groupId) {
                    // 实现查看提供商详情的逻辑
                    alert(`查看提供商 ${groupId} 的详细信息`);
                },

                exportHealthReport() {
                    // 实现导出健康报告的逻辑
                    const report = {
                        timestamp: new Date().toISOString(),
                        system_health: this.systemHealth,
                        provider_statuses: this.providerStatuses
                    };
                    
                    const blob = new Blob([JSON.stringify(report, null, 2)], { type: 'application/json' });
                    const url = URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.href = url;
                    a.download = `health_report_${new Date().toISOString().split('T')[0]}.json`;
                    document.body.appendChild(a);
                    a.click();
                    document.body.removeChild(a);
                    URL.revokeObjectURL(url);
                },

                viewSystemLogs() {
                    window.location.href = '/logs';
                }
            }
        }

        function logout() {
            fetch('/admin/logout', { method: 'POST' })
                .then(() => {
                    window.location.href = '/';
                })
                .catch(error => {
                    console.error('Logout error:', error);
                    window.location.href = '/';
                });
        }
    </script>
</body>
</html>
