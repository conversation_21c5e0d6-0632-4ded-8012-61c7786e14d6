<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{.title}}</title>
    <link rel="icon" type="image/svg+xml" href="/favicon.svg">
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js" defer></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</head>
<body class="bg-gray-100 min-h-screen">
    <div class="container mx-auto px-4 py-8">
        <!-- Header -->
        <div class="flex justify-between items-center mb-8">
            <div>
                <h1 class="text-3xl font-bold text-gray-800">TurnsAPI 仪表板</h1>
                <p class="text-gray-600">实时监控API密钥状态和服务性能</p>
            </div>
            <div class="flex space-x-4">
                <a href="/" class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg transition duration-200">
                    返回首页
                </a>
                <a href="/logs" class="bg-purple-500 hover:bg-purple-600 text-white px-4 py-2 rounded-lg transition duration-200">
                    请求日志
                </a>
                <button onclick="location.reload()" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg transition duration-200">
                    刷新数据
                </button>
                <button onclick="logout()" class="bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded-lg transition duration-200">
                    登出
                </button>
            </div>
        </div>

        <!-- Stats Overview -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <div class="bg-white rounded-lg shadow-md p-6">
                <div class="flex items-center">
                    <div class="p-2 bg-green-100 rounded-lg">
                        <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">活跃密钥</p>
                        <p class="text-2xl font-semibold text-gray-900">{{.active_count}}/{{.total_count}}</p>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-lg shadow-md p-6">
                <div class="flex items-center">
                    <div class="p-2 bg-blue-100 rounded-lg">
                        <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
                        </svg>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">总请求数</p>
                        <p class="text-2xl font-semibold text-gray-900">{{.total_usage}}</p>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-lg shadow-md p-6">
                <div class="flex items-center">
                    <div class="p-2 bg-red-100 rounded-lg">
                        <svg class="w-6 h-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">错误次数</p>
                        <p class="text-2xl font-semibold text-gray-900">{{.total_errors}}</p>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-lg shadow-md p-6">
                <div class="flex items-center">
                    <div class="p-2 bg-purple-100 rounded-lg">
                        <svg class="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">运行时间</p>
                        <p class="text-2xl font-semibold text-gray-900" id="uptime">--</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- API Keys Status -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-8" x-data="keyStatusManagement()">
            <div class="flex justify-between items-center mb-6">
                <h2 class="text-xl font-bold">API 密钥状态</h2>
                <div class="flex space-x-2">
                    <button @click="exportKeys()" class="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg transition duration-200">
                        导出密钥
                    </button>
                    <button @click="refreshKeys()" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg transition duration-200">
                        刷新
                    </button>
                </div>
            </div>

            <!-- 搜索和筛选 -->
            <div class="mb-4 flex space-x-4">
                <div class="flex-1">
                    <input type="text" x-model="searchQuery" @input="filterKeys()"
                           placeholder="搜索密钥名称或密钥..."
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                </div>
                <select x-model="statusFilter" @change="filterKeys()"
                        class="px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                    <option value="">所有状态</option>
                    <option value="active">活跃</option>
                    <option value="inactive">禁用</option>
                </select>
            </div>

            <div class="overflow-x-auto">
                <table class="min-w-full table-auto">
                    <thead>
                        <tr class="bg-gray-50">
                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">密钥</th>
                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">名称</th>
                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">状态</th>
                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">使用次数</th>
                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">错误次数</th>
                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">最后使用</th>
                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">最后错误</th>
                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        <template x-for="(key, index) in paginatedKeys" :key="key.key || key.Key || index">
                            <tr>
                                <td class="px-4 py-4 whitespace-nowrap">
                                    <code class="text-sm bg-gray-100 px-2 py-1 rounded cursor-pointer"
                                          :title="key.key || key.Key"
                                          @click="showFullKey(key.key || key.Key)"
                                          x-text="(key.key || key.Key || '').substring(0, 20) + '...'"></code>
                                </td>
                                <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-900" x-text="key.name || key.Name || '未命名'"></td>
                                <td class="px-4 py-4 whitespace-nowrap">
                                    <span x-show="key.is_active || key.IsActive" class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                        <svg class="w-2 h-2 mr-1" fill="currentColor" viewBox="0 0 8 8">
                                            <circle cx="4" cy="4" r="3"/>
                                        </svg>
                                        活跃
                                    </span>
                                    <span x-show="!(key.is_active || key.IsActive)" class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                        <svg class="w-2 h-2 mr-1" fill="currentColor" viewBox="0 0 8 8">
                                            <circle cx="4" cy="4" r="3"/>
                                        </svg>
                                        禁用
                                    </span>
                                </td>
                                <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-900" x-text="key.usage_count || key.UsageCount || 0"></td>
                                <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-900" x-text="key.error_count || key.ErrorCount || 0"></td>
                                <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-500" x-text="formatDate(key.last_used || key.LastUsed)"></td>
                                <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-500">
                                    <span x-show="key.last_error || key.LastError" class="text-red-600 truncate max-w-xs cursor-pointer"
                                          :title="key.last_error || key.LastError"
                                          x-text="(key.last_error || key.LastError) ? ((key.last_error || key.LastError).length > 30 ? (key.last_error || key.LastError).substring(0, 30) + '...' : (key.last_error || key.LastError)) : ''"></span>
                                    <span x-show="!(key.last_error || key.LastError)">无</span>
                                </td>
                                <td class="px-4 py-4 whitespace-nowrap text-sm font-medium">
                                    <button @click="editKeyFromStatus(key)" class="text-indigo-600 hover:text-indigo-900 mr-3">编辑</button>
                                    <button @click="deleteKeyFromStatus(key.key_id || key.KeyID || key.key || key.Key)" class="text-red-600 hover:text-red-900">删除</button>
                                </td>
                            </tr>
                        </template>
                    </tbody>
                </table>
            </div>

            <!-- 分页 -->
            <div class="mt-4 flex justify-between items-center">
                <div class="text-sm text-gray-700">
                    显示 <span x-text="(currentPage - 1) * pageSize + 1"></span> 到 <span x-text="Math.min(currentPage * pageSize, filteredKeys.length)"></span> 条，共 <span x-text="filteredKeys.length"></span> 条记录
                </div>
                <div class="flex space-x-2">
                    <button @click="previousPage()" :disabled="currentPage === 1"
                            class="px-3 py-1 border border-gray-300 rounded-lg disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50">
                        上一页
                    </button>
                    <span class="px-3 py-1 bg-blue-500 text-white rounded-lg" x-text="currentPage"></span>
                    <button @click="nextPage()" :disabled="currentPage >= totalPages"
                            class="px-3 py-1 border border-gray-300 rounded-lg disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50">
                        下一页
                    </button>
                </div>
            </div>
        </div>

        <!-- API Key Management -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-8" x-data="keyManagement()">
            <div class="flex justify-between items-center mb-6">
                <h2 class="text-xl font-bold">API 密钥管理</h2>
                <div class="space-x-2">
                    <button @click="showAddKeyModal = true" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg transition duration-200">
                        添加密钥
                    </button>
                    <button @click="showBatchAddModal = true" class="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg transition duration-200">
                        批量添加
                    </button>
                </div>
            </div>

            <!-- Add Key Modal -->
            <div x-show="showAddKeyModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50" x-cloak>
                <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
                    <div class="mt-3">
                        <h3 class="text-lg font-medium text-gray-900 mb-4">添加新的 API 密钥</h3>
                        <form @submit.prevent="addKey()">
                            <div class="mb-4">
                                <label class="block text-sm font-medium text-gray-700 mb-2">API 密钥</label>
                                <input type="text" x-model="newKey.key" required
                                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                       placeholder="sk-or-...">
                            </div>
                            <div class="mb-4">
                                <label class="block text-sm font-medium text-gray-700 mb-2">名称</label>
                                <input type="text" x-model="newKey.name"
                                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                       placeholder="密钥名称">
                            </div>
                            <div class="mb-4">
                                <label class="block text-sm font-medium text-gray-700 mb-2">描述</label>
                                <textarea x-model="newKey.description"
                                          class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                          placeholder="密钥描述"></textarea>
                            </div>
                            <div class="mb-4">
                                <label class="block text-sm font-medium text-gray-700 mb-2">允许的模型</label>
                                <div class="text-xs text-gray-500 mb-2">留空表示允许所有模型</div>

                                <!-- 自定义下拉选择器 -->
                                <div class="relative" x-data="{
                                    isOpen: false,
                                    searchTerm: '',
                                    get filteredModels() {
                                        if (!this.searchTerm) return availableModels;
                                        return availableModels.filter(model =>
                                            model.id.toLowerCase().includes(this.searchTerm.toLowerCase())
                                        );
                                    }
                                }">
                                    <!-- 选择框显示区域 -->
                                    <div class="w-full px-3 py-2 border border-gray-300 rounded-md bg-white cursor-pointer min-h-[40px] flex items-center justify-between"
                                         @click="isOpen = !isOpen">
                                        <div class="flex flex-wrap gap-1">
                                            <template x-for="modelId in selectedModels" :key="modelId">
                                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                                    <span x-text="modelId"></span>
                                                    <button type="button" @click.stop="removeModel(modelId)" class="ml-1 text-blue-600 hover:text-blue-800">
                                                        ×
                                                    </button>
                                                </span>
                                            </template>
                                            <span x-show="selectedModels.length === 0" class="text-gray-500">选择模型...</span>
                                        </div>
                                        <div class="flex items-center space-x-1">
                                            <button type="button" x-show="selectedModels.length > 0" @click.stop="clearAllModels()"
                                                    class="text-gray-400 hover:text-gray-600" title="清空所有">
                                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                                </svg>
                                            </button>
                                            <svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                            </svg>
                                        </div>
                                    </div>

                                    <!-- 下拉选项区域 -->
                                    <div x-show="isOpen" x-cloak @click.away="isOpen = false"
                                         class="absolute z-10 w-full mt-1 bg-white border border-gray-300 rounded-md shadow-lg max-h-60 overflow-hidden">
                                        <!-- 搜索框 -->
                                        <div class="p-2 border-b border-gray-200">
                                            <input type="text" x-model="searchTerm"
                                                   placeholder="搜索模型..."
                                                   class="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500">
                                        </div>

                                        <!-- 选项列表 -->
                                        <div class="max-h-48 overflow-y-auto">
                                            <template x-for="model in filteredModels" :key="model.id">
                                                <div class="px-3 py-2 hover:bg-gray-100 cursor-pointer flex items-center justify-between"
                                                     @click="toggleModelSelection(model.id)">
                                                    <span class="text-sm" x-text="model.id"></span>
                                                    <svg x-show="selectedModels.includes(model.id)" class="w-4 h-4 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                                                        <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                                    </svg>
                                                </div>
                                            </template>
                                            <div x-show="filteredModels.length === 0" class="px-3 py-2 text-sm text-gray-500">
                                                没有找到匹配的模型
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="text-xs text-gray-500 mt-1">
                                    已选择 <span x-text="selectedModels.length"></span> 个模型
                                </div>
                            </div>
                            <div class="flex justify-end space-x-3">
                                <button type="button" @click="showAddKeyModal = false"
                                        class="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400">
                                    取消
                                </button>
                                <button type="submit"
                                        class="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600">
                                    添加
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>

            <!-- Edit Key Modal -->
            <div x-show="showEditKeyModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50" x-cloak>
                <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
                    <div class="mt-3">
                        <h3 class="text-lg font-medium text-gray-900 mb-4">编辑 API 密钥</h3>
                        <form @submit.prevent="updateKey()">
                            <div class="mb-4">
                                <label class="block text-sm font-medium text-gray-700 mb-2">API 密钥</label>
                                <div class="flex">
                                    <input type="text" x-model="newKey.key" required
                                           class="flex-1 px-3 py-2 border border-gray-300 rounded-l-md focus:outline-none focus:ring-2 focus:ring-blue-500 font-mono text-sm"
                                           placeholder="sk-or-...">
                                    <button type="button" @click="copyToClipboard(newKey.key)"
                                            class="px-3 py-2 bg-gray-500 text-white rounded-r-md hover:bg-gray-600 focus:outline-none">
                                        复制
                                    </button>
                                </div>
                                <div class="text-xs text-gray-500 mt-1">
                                    修改API密钥将替换原有密钥，请确保新密钥有效
                                </div>
                            </div>
                            <div class="mb-4">
                                <label class="block text-sm font-medium text-gray-700 mb-2">名称</label>
                                <input type="text" x-model="newKey.name"
                                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                       placeholder="密钥名称">
                            </div>
                            <div class="mb-4">
                                <label class="block text-sm font-medium text-gray-700 mb-2">描述</label>
                                <textarea x-model="newKey.description"
                                          class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                          placeholder="密钥描述"></textarea>
                            </div>
                            <div class="mb-4">
                                <label class="block text-sm font-medium text-gray-700 mb-2">允许的模型</label>
                                <div class="text-xs text-gray-500 mb-2">留空表示允许所有模型</div>

                                <!-- 自定义下拉选择器 -->
                                <div class="relative" x-data="{
                                    isOpen: false,
                                    searchTerm: '',
                                    get filteredModels() {
                                        if (!this.searchTerm) return availableModels;
                                        return availableModels.filter(model =>
                                            model.id.toLowerCase().includes(this.searchTerm.toLowerCase())
                                        );
                                    }
                                }">
                                    <!-- 选择框显示区域 -->
                                    <div class="w-full px-3 py-2 border border-gray-300 rounded-md bg-white cursor-pointer min-h-[40px] flex items-center justify-between"
                                         @click="isOpen = !isOpen">
                                        <div class="flex flex-wrap gap-1">
                                            <template x-for="modelId in selectedModels" :key="modelId">
                                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                                    <span x-text="modelId"></span>
                                                    <button type="button" @click.stop="removeModel(modelId)" class="ml-1 text-blue-600 hover:text-blue-800">
                                                        ×
                                                    </button>
                                                </span>
                                            </template>
                                            <span x-show="selectedModels.length === 0" class="text-gray-500">选择模型...</span>
                                        </div>
                                        <div class="flex items-center space-x-1">
                                            <button type="button" x-show="selectedModels.length > 0" @click.stop="clearAllModels()"
                                                    class="text-gray-400 hover:text-gray-600" title="清空所有">
                                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                                </svg>
                                            </button>
                                            <svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                            </svg>
                                        </div>
                                    </div>

                                    <!-- 下拉选项区域 -->
                                    <div x-show="isOpen" x-cloak @click.away="isOpen = false"
                                         class="absolute z-10 w-full mt-1 bg-white border border-gray-300 rounded-md shadow-lg max-h-60 overflow-hidden">
                                        <!-- 搜索框 -->
                                        <div class="p-2 border-b border-gray-200">
                                            <input type="text" x-model="searchTerm"
                                                   placeholder="搜索模型..."
                                                   class="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500">
                                        </div>

                                        <!-- 选项列表 -->
                                        <div class="max-h-48 overflow-y-auto">
                                            <template x-for="model in filteredModels" :key="model.id">
                                                <div class="px-3 py-2 hover:bg-gray-100 cursor-pointer flex items-center justify-between"
                                                     @click="toggleModelSelection(model.id)">
                                                    <span class="text-sm" x-text="model.id"></span>
                                                    <svg x-show="selectedModels.includes(model.id)" class="w-4 h-4 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                                                        <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                                    </svg>
                                                </div>
                                            </template>
                                            <div x-show="filteredModels.length === 0" class="px-3 py-2 text-sm text-gray-500">
                                                没有找到匹配的模型
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="text-xs text-gray-500 mt-1">
                                    已选择 <span x-text="selectedModels.length"></span> 个模型
                                </div>
                            </div>
                            <div class="flex justify-end space-x-3">
                                <button type="button" @click="showEditKeyModal = false"
                                        class="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400">
                                    取消
                                </button>
                                <button type="submit"
                                        class="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600">
                                    更新
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>

            <!-- Batch Add Keys Modal -->
            <div x-show="showBatchAddModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50" x-cloak>
                <div class="relative top-10 mx-auto p-5 border w-2/3 max-w-4xl shadow-lg rounded-md bg-white">
                    <div class="mt-3">
                        <h3 class="text-lg font-medium text-gray-900 mb-4">批量添加 API 密钥</h3>
                        <form @submit.prevent="batchAddKeys()">
                            <div class="mb-4">
                                <label class="block text-sm font-medium text-gray-700 mb-2">API 密钥列表</label>
                                <textarea x-model="batchKeys" required rows="10"
                                          class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"
                                          placeholder="请输入API密钥，每行一个：&#10;sk-or-v1-...&#10;sk-or-v1-...&#10;sk-or-v1-..."></textarea>
                                <div class="text-xs text-gray-500 mt-1">
                                    每行输入一个API密钥，空行将被忽略
                                </div>
                            </div>
                            <div class="mb-4" x-show="batchResult">
                                <div class="p-3 rounded-md" :class="batchResult && batchResult.success ? 'bg-green-50 border border-green-200' : 'bg-red-50 border border-red-200'">
                                    <p class="text-sm font-medium" :class="batchResult && batchResult.success ? 'text-green-800' : 'text-red-800'" x-text="batchResult ? batchResult.message : ''"></p>
                                    <div x-show="batchResult && batchResult.errors && batchResult.errors.length > 0" class="mt-2">
                                        <p class="text-xs text-red-600 mb-1">错误详情：</p>
                                        <ul class="text-xs text-red-600 list-disc list-inside">
                                            <template x-for="error in (batchResult ? batchResult.errors : [])">
                                                <li x-text="error"></li>
                                            </template>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                            <div class="flex justify-end space-x-3">
                                <button type="button" @click="closeBatchAddModal()"
                                        class="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400">
                                    取消
                                </button>
                                <button type="submit"
                                        class="px-4 py-2 bg-green-500 text-white rounded-md hover:bg-green-600">
                                    批量添加
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>

        <!-- Proxy Key Management -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-8" x-data="proxyKeyManagement()">
            <div class="flex justify-between items-center mb-6">
                <h2 class="text-xl font-bold">代理服务 API 密钥</h2>
                <button @click="showGenerateKeyModal = true" class="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg transition duration-200">
                    生成密钥
                </button>
            </div>

            <!-- Proxy Keys Table -->
            <div class="overflow-x-auto">
                <table class="min-w-full table-auto">
                    <thead>
                        <tr class="bg-gray-50">
                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">密钥</th>
                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">名称</th>
                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">使用次数</th>
                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">创建时间</th>
                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        <template x-for="key in proxyKeys" :key="key.id">
                            <tr>
                                <td class="px-4 py-4 whitespace-nowrap">
                                    <code class="text-sm bg-gray-100 px-2 py-1 rounded" x-text="key.key"></code>
                                </td>
                                <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-900" x-text="key.name"></td>
                                <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-900" x-text="key.usage_count"></td>
                                <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-500" x-text="formatDate(key.created_at)"></td>
                                <td class="px-4 py-4 whitespace-nowrap text-sm font-medium">
                                    <button @click="deleteProxyKey(key.id)"
                                            class="text-red-600 hover:text-red-900">删除</button>
                                </td>
                            </tr>
                        </template>
                    </tbody>
                </table>
            </div>

            <!-- Generate Key Modal -->
            <div x-show="showGenerateKeyModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50" x-cloak>
                <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
                    <div class="mt-3">
                        <h3 class="text-lg font-medium text-gray-900 mb-4">生成代理服务 API 密钥</h3>
                        <form @submit.prevent="generateProxyKey()">
                            <div class="mb-4">
                                <label class="block text-sm font-medium text-gray-700 mb-2">名称</label>
                                <input type="text" x-model="newProxyKey.name" required
                                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"
                                       placeholder="密钥名称">
                            </div>
                            <div class="mb-4">
                                <label class="block text-sm font-medium text-gray-700 mb-2">描述</label>
                                <textarea x-model="newProxyKey.description"
                                          class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"
                                          placeholder="密钥描述"></textarea>
                            </div>
                            <div class="flex justify-end space-x-3">
                                <button type="button" @click="showGenerateKeyModal = false"
                                        class="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400">
                                    取消
                                </button>
                                <button type="submit"
                                        class="px-4 py-2 bg-green-500 text-white rounded-md hover:bg-green-600">
                                    生成
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>

        <!-- Real-time Updates -->
        <div class="bg-white rounded-lg shadow-md p-6" x-data="dashboard()">
            <h2 class="text-xl font-bold mb-6">实时状态</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <h3 class="text-lg font-medium mb-4">服务状态</h3>
                    <div class="space-y-2">
                        <div class="flex justify-between">
                            <span>服务状态:</span>
                            <span class="font-medium" :class="status.healthy ? 'text-green-600' : 'text-red-600'" x-text="status.healthy ? '正常' : '异常'"></span>
                        </div>
                        <div class="flex justify-between">
                            <span>最后更新:</span>
                            <span class="text-sm text-gray-500" x-text="status.lastUpdate"></span>
                        </div>
                    </div>
                </div>
                <div>
                    <h3 class="text-lg font-medium mb-4">操作</h3>
                    <div class="space-y-2">
                        <button @click="refreshStatus()" class="w-full bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg transition duration-200">
                            刷新状态
                        </button>
                        <!-- 移除自动刷新按钮 -->
                        <!-- <button @click="toggleAutoRefresh()" class="w-full border border-gray-300 hover:bg-gray-50 px-4 py-2 rounded-lg transition duration-200" :class="autoRefresh ? 'bg-green-50 border-green-300' : ''">
                            <span x-text="autoRefresh ? '停止自动刷新' : '开启自动刷新'"></span>
                        </button> -->
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 密钥状态管理组件
        function keyStatusManagement() {
            return {
                keys: [],
                filteredKeys: [],
                paginatedKeys: [],
                searchQuery: '',
                statusFilter: '',
                currentPage: 1,
                pageSize: 10,
                totalPages: 1,

                async init() {
                    await this.loadKeys();
                },

                async loadKeys() {
                    try {
                        const response = await fetch('/admin/keys');
                        const result = await response.json();
                        if (result.keys && Array.isArray(result.keys)) {
                            this.keys = result.keys;
                        } else {
                            this.keys = [];
                        }
                        this.filterKeys();
                    } catch (error) {
                        console.error('Load keys error:', error);
                        this.keys = [];
                        this.filterKeys();
                    }
                },

                async refreshKeys() {
                    await this.loadKeys();
                },

                filterKeys() {
                    // 确保keys是数组
                    if (!Array.isArray(this.keys)) {
                        this.keys = [];
                    }

                    let filtered = [...this.keys];

                    // 搜索过滤
                    if (this.searchQuery) {
                        const query = this.searchQuery.toLowerCase();
                        filtered = filtered.filter(key =>
                            ((key.key || key.Key || '').toLowerCase().includes(query)) ||
                            ((key.name || key.Name || '').toLowerCase().includes(query))
                        );
                    }

                    // 状态过滤
                    if (this.statusFilter) {
                        if (this.statusFilter === 'active') {
                            filtered = filtered.filter(key => key.is_active || key.IsActive);
                        } else if (this.statusFilter === 'inactive') {
                            filtered = filtered.filter(key => !(key.is_active || key.IsActive));
                        }
                    }

                    this.filteredKeys = filtered;
                    this.totalPages = Math.ceil(this.filteredKeys.length / this.pageSize) || 1;
                    this.currentPage = 1;
                    this.updatePaginatedKeys();
                },

                updatePaginatedKeys() {
                    const start = (this.currentPage - 1) * this.pageSize;
                    const end = start + this.pageSize;
                    this.paginatedKeys = this.filteredKeys.slice(start, end);
                },

                previousPage() {
                    if (this.currentPage > 1) {
                        this.currentPage--;
                        this.updatePaginatedKeys();
                    }
                },

                nextPage() {
                    if (this.currentPage < this.totalPages) {
                        this.currentPage++;
                        this.updatePaginatedKeys();
                    }
                },

                showFullKey(key) {
                    alert('完整密钥:\n' + key);
                },

                formatDate(dateStr) {
                    if (!dateStr || dateStr === '0001-01-01T00:00:00Z') {
                        return '从未使用';
                    }
                    return new Date(dateStr).toLocaleString('zh-CN');
                },

                exportKeys() {
                    const csvContent = "data:text/csv;charset=utf-8,"
                        + "密钥,名称,状态,使用次数,错误次数,最后使用,最后错误\n"
                        + this.filteredKeys.map(key => {
                            // 兼容不同的字段命名格式，使用完整密钥（不掩码）
                            const keyValue = key.key_id || key.KeyID || key.key || key.Key || '';
                            const nameValue = key.name || key.Name || '';
                            const isActive = key.is_active !== undefined ? key.is_active : (key.IsActive !== undefined ? key.IsActive : false);
                            const usageCount = key.usage_count !== undefined ? key.usage_count : (key.UsageCount !== undefined ? key.UsageCount : 0);
                            const errorCount = key.error_count !== undefined ? key.error_count : (key.ErrorCount !== undefined ? key.ErrorCount : 0);
                            const lastUsed = key.last_used || key.LastUsed || '';
                            const lastError = key.last_error || key.LastError || '';

                            return `${keyValue},${nameValue},${isActive ? '活跃' : '禁用'},${usageCount},${errorCount},${this.formatDate(lastUsed)},${lastError}`;
                        }).join("\n");

                    const encodedUri = encodeURI(csvContent);
                    const link = document.createElement("a");
                    link.setAttribute("href", encodedUri);
                    link.setAttribute("download", "api_keys_" + new Date().toISOString().split('T')[0] + ".csv");
                    document.body.appendChild(link);
                    link.click();
                    document.body.removeChild(link);
                },

                editKeyFromStatus(key) {
                    // 触发密钥管理组件的编辑功能
                    const keyMgmtElement = document.querySelector('[x-data*="keyManagement"]');
                    if (keyMgmtElement && keyMgmtElement._x_dataStack) {
                        const keyMgmt = keyMgmtElement._x_dataStack[0];
                        if (keyMgmt && keyMgmt.editKey) {
                            const keyId = key.key_id || key.KeyID || key.key || key.Key;
                            const keyName = key.name || key.Name || '';
                            const keyDesc = key.description || key.Description || '';
                            const allowedModels = key.allowed_models || key.AllowedModels || [];
                            keyMgmt.editKey(keyId, keyName, keyDesc, allowedModels);
                        }
                    }
                },

                async deleteKeyFromStatus(keyId) {
                    if (!confirm('确定要删除这个API密钥吗？此操作不可撤销。')) {
                        return;
                    }

                    try {
                        const response = await fetch(`/admin/keys/${encodeURIComponent(keyId)}`, {
                            method: 'DELETE'
                        });

                        const result = await response.json();
                        if (result.success) {
                            alert('API密钥删除成功！');
                            await this.loadKeys();
                        } else {
                            alert('删除失败: ' + (result.error || '未知错误'));
                        }
                    } catch (error) {
                        console.error('Delete key error:', error);
                        alert('网络错误，请检查连接');
                    }
                }
            }
        }

        // API密钥管理组件
        function keyManagement() {
            return {
                showAddKeyModal: false,
                showEditKeyModal: false,
                showBatchAddModal: false,
                availableModels: [],
                selectedModels: [],
                editingKey: null,
                batchKeys: '',
                batchResult: null,
                newKey: {
                    key: '',
                    name: '',
                    description: '',
                    allowed_models: []
                },
                async loadAvailableModels() {
                    try {
                        const response = await fetch('/admin/available-models');
                        const result = await response.json();
                        if (result.data) {
                            this.availableModels = result.data.map(model => ({
                                id: model.id,
                                name: model.name || model.id
                            }));
                        }
                    } catch (error) {
                        console.error('Load models error:', error);
                    }
                },

                // 模型选择相关方法
                toggleModelSelection(modelId) {
                    const index = this.selectedModels.indexOf(modelId);
                    if (index > -1) {
                        this.selectedModels.splice(index, 1);
                    } else {
                        this.selectedModels.push(modelId);
                    }
                },

                removeModel(modelId) {
                    const index = this.selectedModels.indexOf(modelId);
                    if (index > -1) {
                        this.selectedModels.splice(index, 1);
                    }
                },

                clearAllModels() {
                    this.selectedModels = [];
                },

                async addKey() {
                    try {
                        // 确保allowed_models使用selectedModels的值
                        this.newKey.allowed_models = [...this.selectedModels];

                        const response = await fetch('/admin/keys', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                            },
                            body: JSON.stringify(this.newKey)
                        });

                        const result = await response.json();
                        if (result.success) {
                            alert('API密钥添加成功！');
                            this.showAddKeyModal = false;
                            this.newKey = { key: '', name: '', description: '', allowed_models: [] };
                            this.selectedModels = [];
                            location.reload(); // 刷新页面显示新密钥
                        } else {
                            alert('添加失败: ' + (result.error || '未知错误'));
                        }
                    } catch (error) {
                        console.error('Add key error:', error);
                        alert('网络错误，请检查连接');
                    }
                },
                async batchAddKeys() {
                    try {
                        // 解析密钥列表
                        const keys = this.batchKeys.split('\n')
                            .map(key => key.trim())
                            .filter(key => key.length > 0);

                        if (keys.length === 0) {
                            alert('请输入至少一个API密钥');
                            return;
                        }

                        const response = await fetch('/admin/keys/batch', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                            },
                            body: JSON.stringify({ keys: keys })
                        });

                        const result = await response.json();
                        this.batchResult = result;

                        if (result.success) {
                            // 如果全部成功，3秒后自动关闭并刷新
                            if (result.skipped_count === 0) {
                                setTimeout(() => {
                                    this.closeBatchAddModal();
                                    location.reload();
                                }, 3000);
                            }
                        }
                    } catch (error) {
                        console.error('Batch add keys error:', error);
                        this.batchResult = {
                            success: false,
                            message: '网络错误，请检查连接'
                        };
                    }
                },
                closeBatchAddModal() {
                    this.showBatchAddModal = false;
                    this.batchKeys = '';
                    this.batchResult = null;
                },
                async editKey(keyId, name, description, allowedModels = []) {
                    this.editingKey = keyId;
                    this.newKey = {
                        key: keyId, // 保持完整的密钥用于显示和编辑
                        name: name || '',
                        description: description || '',
                        allowed_models: allowedModels || []
                    };
                    this.selectedModels = [...(allowedModels || [])];
                    this.showEditKeyModal = true;
                    await this.loadAvailableModels(); // 加载可用模型
                },
                async updateKey() {
                    try {
                        const response = await fetch(`/admin/keys/${encodeURIComponent(this.editingKey)}`, {
                            method: 'PUT',
                            headers: {
                                'Content-Type': 'application/json',
                            },
                            body: JSON.stringify({
                                key: this.newKey.key,
                                name: this.newKey.name,
                                description: this.newKey.description,
                                allowed_models: this.selectedModels
                            })
                        });

                        const result = await response.json();
                        if (result.success) {
                            alert('API密钥更新成功！');
                            this.showEditKeyModal = false;
                            this.editingKey = null;
                            this.newKey = { key: '', name: '', description: '', allowed_models: [] };
                            this.selectedModels = [];
                            location.reload();
                        } else {
                            alert('更新失败: ' + (result.error || '未知错误'));
                        }
                    } catch (error) {
                        console.error('Update key error:', error);
                        alert('网络错误，请检查连接');
                    }
                },
                async deleteKey(keyId) {
                    if (!confirm('确定要删除这个API密钥吗？此操作不可撤销。')) {
                        return;
                    }

                    try {
                        const response = await fetch(`/admin/keys/${encodeURIComponent(keyId)}`, {
                            method: 'DELETE'
                        });

                        const result = await response.json();
                        if (result.success) {
                            alert('API密钥删除成功！');
                            location.reload();
                        } else {
                            alert('删除失败: ' + (result.error || '未知错误'));
                        }
                    } catch (error) {
                        console.error('Delete key error:', error);
                        alert('网络错误，请检查连接');
                    }
                },
                copyToClipboard(text) {
                    if (navigator.clipboard) {
                        navigator.clipboard.writeText(text).then(() => {
                            alert('已复制到剪贴板');
                        }).catch(err => {
                            console.error('复制失败:', err);
                            this.fallbackCopyTextToClipboard(text);
                        });
                    } else {
                        this.fallbackCopyTextToClipboard(text);
                    }
                },
                fallbackCopyTextToClipboard(text) {
                    const textArea = document.createElement("textarea");
                    textArea.value = text;
                    textArea.style.top = "0";
                    textArea.style.left = "0";
                    textArea.style.position = "fixed";
                    document.body.appendChild(textArea);
                    textArea.focus();
                    textArea.select();
                    try {
                        const successful = document.execCommand('copy');
                        if (successful) {
                            alert('已复制到剪贴板');
                        } else {
                            alert('复制失败，请手动复制');
                        }
                    } catch (err) {
                        console.error('Fallback: 复制失败', err);
                        alert('复制失败，请手动复制');
                    }
                    document.body.removeChild(textArea);
                },
                init() {
                    this.loadAvailableModels();
                }
            }
        }

        // 代理密钥管理组件
        function proxyKeyManagement() {
            return {
                showGenerateKeyModal: false,
                proxyKeys: [],
                newProxyKey: {
                    name: '',
                    description: ''
                },
                async init() {
                    await this.loadProxyKeys();
                },
                async loadProxyKeys() {
                    try {
                        const response = await fetch('/admin/proxy-keys');
                        const result = await response.json();
                        if (result.success) {
                            this.proxyKeys = result.keys || [];
                        }
                    } catch (error) {
                        console.error('Load proxy keys error:', error);
                    }
                },
                async generateProxyKey() {
                    try {
                        const response = await fetch('/admin/proxy-keys', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                            },
                            body: JSON.stringify(this.newProxyKey)
                        });

                        const result = await response.json();
                        if (result.success) {
                            alert('代理服务API密钥生成成功！\n密钥: ' + result.key.key);
                            this.showGenerateKeyModal = false;
                            this.newProxyKey = { name: '', description: '' };
                            await this.loadProxyKeys(); // 重新加载列表
                        } else {
                            alert('生成失败: ' + (result.error || '未知错误'));
                        }
                    } catch (error) {
                        console.error('Generate proxy key error:', error);
                        alert('网络错误，请检查连接');
                    }
                },
                async deleteProxyKey(keyId) {
                    if (!confirm('确定要删除这个代理服务API密钥吗？')) {
                        return;
                    }

                    try {
                        const response = await fetch(`/admin/proxy-keys/${keyId}`, {
                            method: 'DELETE'
                        });

                        const result = await response.json();
                        if (result.success) {
                            alert('代理服务API密钥删除成功！');
                            await this.loadProxyKeys(); // 重新加载列表
                        } else {
                            alert('删除失败: ' + (result.error || '未知错误'));
                        }
                    } catch (error) {
                        console.error('Delete proxy key error:', error);
                        alert('网络错误，请检查连接');
                    }
                },
                formatDate(dateStr) {
                    if (!dateStr) return '未知';
                    const date = new Date(dateStr);
                    return date.toLocaleString('zh-CN');
                }
            }
        }

        function dashboard() {
            return {
                status: {
                    healthy: true,
                    lastUpdate: new Date().toLocaleString()
                },
                // 移除自动刷新相关变量
                // autoRefresh: false,
                // refreshInterval: null,
                
                async refreshStatus() {
                    try {
                        const response = await fetch('/admin/status');
                        const data = await response.json();
                        this.status.healthy = response.ok;
                        this.status.lastUpdate = new Date().toLocaleString();
                    } catch (error) {
                        this.status.healthy = false;
                        this.status.lastUpdate = new Date().toLocaleString();
                    }
                },
                
                // 移除自动刷新功能
                // toggleAutoRefresh() {
                //     this.autoRefresh = !this.autoRefresh;
                //     if (this.autoRefresh) {
                //         this.refreshInterval = setInterval(() => {
                //             this.refreshStatus();
                //         }, 30000); // 30秒刷新一次
                //     } else {
                //         if (this.refreshInterval) {
                //             clearInterval(this.refreshInterval);
                //             this.refreshInterval = null;
                //         }
                //     }
                // },
                
                init() {
                    this.refreshStatus();
                }
            }
        }
        
        // 更新运行时间显示
        function updateUptime() {
            let serverStartTime = null;

            // 获取服务器启动时间
            async function getServerStartTime() {
                try {
                    const response = await fetch('/admin/status');
                    const data = await response.json();
                    if (data.uptime) {
                        // 计算服务器启动时间
                        const uptimeMs = parseFloat(data.uptime) / 1000000; // Go的Duration是纳秒，转换为毫秒
                        serverStartTime = new Date(Date.now() - uptimeMs);
                    }
                } catch (error) {
                    console.error('Failed to get server start time:', error);
                }
            }

            // 初始化获取服务器启动时间
            getServerStartTime();

            // 移除定时更新运行时间
            // setInterval(() => {
            //     if (serverStartTime) {
            //         const now = new Date();
            //         const diff = now - serverStartTime;
            //         const hours = Math.floor(diff / (1000 * 60 * 60));
            //         const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));
            //         const seconds = Math.floor((diff % (1000 * 60)) / 1000);
            //         document.getElementById('uptime').textContent = `${hours}h ${minutes}m ${seconds}s`;
            //     } else {
            //         document.getElementById('uptime').textContent = '--';
            //     }
            // }, 1000);
        }
        
        // 登出函数
        async function logout() {
            try {
                const response = await fetch('/auth/logout', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    }
                });

                if (response.ok) {
                    window.location.href = '/auth/login';
                } else {
                    alert('登出失败，请重试');
                }
            } catch (error) {
                console.error('Logout error:', error);
                alert('网络错误，请检查连接');
            }
        }

        // 全局编辑和删除函数
        function editKey(keyId, name, description, allowedModels) {
            // 获取keyManagement组件实例并调用editKey方法
            const keyMgmtElement = document.querySelector('[x-data*="keyManagement"]');
            if (keyMgmtElement && keyMgmtElement._x_dataStack) {
                const keyMgmt = keyMgmtElement._x_dataStack[0];
                if (keyMgmt && keyMgmt.editKey) {
                    keyMgmt.editKey(keyId, name, description, allowedModels ? allowedModels.split(',') : []);
                }
            }
        }

        function deleteKey(keyId) {
            // 获取keyManagement组件实例并调用deleteKey方法
            const keyMgmtElement = document.querySelector('[x-data*="keyManagement"]');
            if (keyMgmtElement && keyMgmtElement._x_dataStack) {
                const keyMgmt = keyMgmtElement._x_dataStack[0];
                if (keyMgmt && keyMgmt.deleteKey) {
                    keyMgmt.deleteKey(keyId);
                }
            }
        }

        // 页面加载完成后启动运行时间计时器
        document.addEventListener('DOMContentLoaded', function() {
            updateUptime();

            // 手动初始化keyStatusManagement组件
            setTimeout(() => {
                const keyStatusElement = document.querySelector('[x-data*="keyStatusManagement"]');
                if (keyStatusElement && keyStatusElement._x_dataStack && keyStatusElement._x_dataStack[0]) {
                    const component = keyStatusElement._x_dataStack[0];
                    if (component.init) {
                        component.init();
                    }
                }
            }, 100);
        });
    </script>
</body>
</html>
